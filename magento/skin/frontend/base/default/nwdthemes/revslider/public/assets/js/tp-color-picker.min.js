!function(e){window.RevColor={defaultValue:"#ffffff",isColor:/(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i,get:function(e){return e?RevColor.process(e,!0)[0]:"transparent"},parse:function(e,r,a){e=RevColor.process(e,!0);var t=[];return t[0]=r?r+": "+e[0]+";":e[0],a&&(t[1]=e[1]),t},convert:function(e,r){if(!e||"string"!=typeof e)return RevColor.defaultValue;if("transparent"===e)return e;if(-1!==e.search(/\[\{/)||-1!==e.search("gradient"))return RevColor.process(e,!0)[0];if(void 0===r||isNaN(r))return RevColor.process(e,!0)[0];if((r=parseFloat(r))<=1&&(r*=100),0===(r=Math.max(Math.min(parseInt(r,10),100),0)))return"transparent";try{return-1!==e.search("#")||e.length<8?(RevColor.isColor.test(e)||(e=e.replace(/[^A-Za-z0-9#]/g,"")),RevColor.processRgba(RevColor.sanitizeHex(e),r)):(e=RevColor.rgbValues(e,3),RevColor.rgbaString(e[0],e[1],e[2],.01*r))}catch(e){return RevColor.defaultValue}},process:function(e,r){if("string"!=typeof e)return r&&(e=RevColor.sanitizeGradient(e)),[RevColor.processGradient(e),"gradient",e];if("transparent"===e.trim())return["transparent","transparent"];if(-1===e.search(/\[\{/))return-1!==e.search("#")?[RevColor.sanitizeHex(e),"hex"]:-1!==e.search("rgba")?[e.replace(/\s/g,"").replace(/false/g,"1"),"rgba"]:-1!==e.search("rgb")?[e.replace(/\s/g,""),"rgb"]:RevColor.isColor.test(e)?[e,"hex"]:["transparent","transparent",!0];try{return e=JSON.parse(e.replace(/\&/g,'"')),r&&(e=RevColor.sanitizeGradient(e)),[RevColor.processGradient(e),"gradient",e]}catch(e){return console.log("RevColor.process() failed to parse JSON string"),["linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)","gradient",{type:"linear",angle:"0",colors:[{r:"255",g:"255",b:"255",a:"1",position:"0",align:"bottom"},{r:"0",g:"0",b:"0",a:"1",position:"100",align:"bottom"}]}]}},transparentRgba:function(e,r){if(!r&&"rgba"!==RevColor.process(e)[1])return!1;return"0"===RevColor.rgbValues(e,4)[3]},rgbValues:function(e,r){3===(e=e.substring(e.indexOf("(")+1,e.lastIndexOf(")")).split(",")).length&&4===r&&(e[3]="1");for(var a=0;a<r;a++)e[a]=e[a].trim();return e},rgbaString:function(e,r,a,t){return"rgba("+e+", "+r+", "+a+", "+t+")"},rgbToHex:function(e){var r=RevColor.rgbValues(e,3);return RevColor.getRgbToHex(r[0],r[1],r[2])},rgbaToHex:function(e){var r=RevColor.rgbValues(e,4);return[RevColor.getRgbToHex(r[0],r[1],r[2]),r[3]]},getOpacity:function(e){return parseInt(100*RevColor.rgbValues(e,4)[3],10)+"%"},getRgbToHex:function(e,r,a){return"#"+("0"+parseInt(e).toString(16)).slice(-2)+("0"+parseInt(r).toString(16)).slice(-2)+("0"+parseInt(a).toString(16)).slice(-2)},joinToRgba:function(e){return e=e.split("||"),RevColor.convert(e[0],e[1])},processRgba:function(e,r){e=e.replace("#","");var a=void 0!==r,t=(a?"rgba":"rgb")+"("+parseInt(e.substring(0,2),16)+", "+parseInt(e.substring(2,4),16)+", "+parseInt(e.substring(4,6),16);return t+=a?", "+(.01*parseInt(r,10)).toFixed(2).replace(/\.?0*$/,"")+")":")"},processGradient:function(e){for(var r,a=e.type,t=a+"-gradient(",i="linear"===a?e.angle+"deg, ":"ellipse at center, ",o=e.colors,c=o.length,n="",s=0;s<c;s++)s>0&&(n+=", "),n+="rgba("+(r=o[s]).r+", "+r.g+", "+r.b+", "+r.a+") "+r.position+"%";return t+i+n+")"},sanitizeHex:function(e){if(3===(e=e.replace("#","").trim()).length){var r=e.charAt(0),a=e.charAt(1),t=e.charAt(2);e=r+r+a+a+t+t}return"#"+e},sanitizeGradient:function(e){for(var r,a=e.colors,t=a.length,i=[],o=0;o<t;o++){var c=a[o];delete c.align,r?JSON.stringify(c)!==JSON.stringify(r)&&(i[i.length]=c):i[i.length]=c,r=c}return e.colors=i,e}},function(e){var r,a,t,i,o,c,n,s,l,p,v,d,g,b,k,u,f,m,h,C,x,y,w,R,_,I,N,F,M,j,A,H,S,V,T,E,O,P,z,D,L,B,G,q,$,J,Y,W,X,Q,U,Z,K,ee,re,ae,te,ie,oe,ce,ne,se,le,pe,ve,de,ge,be,ke,ue,fe,me,he,Ce,xe,ye,we,Re,_e,Ie,Ne,Fe,Me,je,Ae={},He=265,Se=20,Ve=30,Te=6,Ee=5,Oe=6,Pe=10,ze=15,De=180,Le={axis:"x",containment:"#rev-cpicker-point-wrap"},Be={color:"Color",solid_color:"Solid Color",gradient_color:"Gradient Color",currently_editing:"Currently Editing",core_presets:"Core Presets",custom_presets:"Custom Presets",enter_a_name:"Enter a Name",save_a_new_preset:"Save a new Preset",save:"Save",color_hex_value:"Color Hex Value",opacity:"Opacity",clear:"Clear",location:"Location",delete:"Delete",horizontal:"Horizontal",vertical:"Vertical",radial:"Radial",enter_angle:"Enter Angle",reverse_gradient:"Reverse Gradient",delete_confirm:"Remove/Delete this custom preset color?",naming_error:"Please enter a unique name for the new color preset"},Ge=["#FFFFFF","#000000","#FF3A2D","#007AFF","#4CD964","#FFCC00","#C7C7CC","#8E8E93","#FFD3E0","#34AADC","#E0F8D8","#FF9500"],qe=[{0:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:247,&g&:247,&b&:247,&a&:&1&,&position&:0,&align&:&top&},{&r&:247,&g&:247,&b&:247,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:215,&g&:215,&b&:215,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:215,&g&:215,&b&:215,&a&:&1&,&position&:100,&align&:&top&}]}"},{1:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:74,&g&:74,&b&:74,&a&:&1&,&position&:0,&align&:&top&},{&r&:74,&g&:74,&b&:74,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:43,&g&:43,&b&:43,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:43,&g&:43,&b&:43,&a&:&1&,&position&:100,&align&:&top&}]}"},{2:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:219,&g&:221,&b&:222,&a&:&1&,&position&:0,&align&:&top&},{&r&:219,&g&:221,&b&:222,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:137,&g&:140,&b&:144,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:137,&g&:140,&b&:144,&a&:&1&,&position&:100,&align&:&top&}]}"},{3:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:26,&g&:214,&b&:253,&a&:&1&,&position&:0,&align&:&top&},{&r&:26,&g&:214,&b&:253,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:29,&g&:98,&b&:240,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:29,&g&:98,&b&:240,&a&:&1&,&position&:100,&align&:&top&}]}"},{4:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:198,&g&:68,&b&:252,&a&:&1&,&position&:0,&align&:&top&},{&r&:198,&g&:68,&b&:252,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:88,&g&:86,&b&:214,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:88,&g&:86,&b&:214,&a&:&1&,&position&:100,&align&:&top&}]}"},{5:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:255,&g&:94,&b&:58,&a&:&1&,&position&:0,&align&:&top&},{&r&:255,&g&:94,&b&:58,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:255,&g&:42,&b&:104,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:42,&b&:104,&a&:&1&,&position&:100,&align&:&top&}]}"},{6:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:228,&g&:221,&b&:202,&a&:&1&,&position&:0,&align&:&top&},{&r&:228,&g&:221,&b&:202,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:214,&g&:206,&b&:195,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:214,&g&:206,&b&:195,&a&:&1&,&position&:100,&align&:&top&}]}"},{7:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:255,&g&:219,&b&:76,&a&:&1&,&position&:0,&align&:&top&},{&r&:255,&g&:219,&b&:76,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:255,&g&:205,&b&:2,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:205,&b&:2,&a&:&1&,&position&:100,&align&:&top&}]}"},{8:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:255,&g&:149,&b&:0,&a&:&1&,&position&:0,&align&:&top&},{&r&:255,&g&:149,&b&:0,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:255,&g&:94,&b&:58,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:94,&b&:58,&a&:&1&,&position&:100,&align&:&top&}]}"},{9:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:82,&g&:237,&b&:199,&a&:&1&,&position&:0,&align&:&top&},{&r&:82,&g&:237,&b&:199,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:90,&g&:200,&b&:251,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:90,&g&:200,&b&:251,&a&:&1&,&position&:100,&align&:&top&}]}"},{10:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:228,&g&:183,&b&:240,&a&:&1&,&position&:0,&align&:&top&},{&r&:228,&g&:183,&b&:240,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:200,&g&:110,&b&:223,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:200,&g&:110,&b&:223,&a&:&1&,&position&:100,&align&:&top&}]}"},{11:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:135,&g&:252,&b&:112,&a&:&1&,&position&:0,&align&:&top&},{&r&:135,&g&:252,&b&:112,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:11,&g&:211,&b&:24,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:11,&g&:211,&b&:24,&a&:&1&,&position&:100,&align&:&top&}]}"},{12:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:61,&g&:78,&b&:129,&a&:&1&,&position&:0,&align&:&top&},{&r&:61,&g&:78,&b&:129,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:87,&g&:83,&b&:201,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:110,&g&:127,&b&:243,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:110,&g&:127,&b&:243,&a&:&1&,&position&:100,&align&:&top&}]}"},{13:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:35,&g&:21,&b&:87,&a&:&1&,&position&:0,&align&:&top&},{&r&:35,&g&:21,&b&:87,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:68,&g&:16,&b&:122,&a&:&1&,&position&:29,&align&:&bottom&},{&r&:255,&g&:19,&b&:97,&a&:&1&,&position&:67,&align&:&bottom&},{&r&:255,&g&:248,&b&:0,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:248,&b&:0,&a&:&1&,&position&:100,&align&:&top&}]}"},{14:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:105,&g&:234,&b&:203,&a&:&1&,&position&:0,&align&:&top&},{&r&:105,&g&:234,&b&:203,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:234,&g&:204,&b&:248,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:102,&g&:84,&b&:241,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:102,&g&:84,&b&:241,&a&:&1&,&position&:100,&align&:&top&}]}"},{15:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:255,&g&:5,&b&:124,&a&:&1&,&position&:0,&align&:&top&},{&r&:255,&g&:5,&b&:124,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:124,&g&:100,&b&:213,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:76,&g&:195,&b&:255,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:76,&g&:195,&b&:255,&a&:&1&,&position&:100,&align&:&top&}]}"},{16:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:255,&g&:5,&b&:124,&a&:&1&,&position&:0,&align&:&top&},{&r&:255,&g&:5,&b&:124,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:141,&g&:11,&b&:147,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:50,&g&:21,&b&:117,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:50,&g&:21,&b&:117,&a&:&1&,&position&:100,&align&:&top&}]}"},{17:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:164,&g&:69,&b&:178,&a&:&1&,&position&:0,&align&:&top&},{&r&:164,&g&:69,&b&:178,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:212,&g&:24,&b&:114,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:255,&g&:0,&b&:102,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:0,&b&:102,&a&:&1&,&position&:100,&align&:&top&}]}"},{18:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:158,&g&:251,&b&:211,&a&:&1&,&position&:0,&align&:&top&},{&r&:158,&g&:251,&b&:211,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:87,&g&:233,&b&:242,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:69,&g&:212,&b&:251,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:69,&g&:212,&b&:251,&a&:&1&,&position&:100,&align&:&top&}]}"},{19:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:172,&g&:50,&b&:228,&a&:&1&,&position&:0,&align&:&top&},{&r&:172,&g&:50,&b&:228,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:121,&g&:24,&b&:242,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:72,&g&:1,&b&:255,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:72,&g&:1,&b&:255,&a&:&1&,&position&:100,&align&:&top&}]}"},{20:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:112,&g&:133,&b&:182,&a&:&1&,&position&:0,&align&:&top&},{&r&:112,&g&:133,&b&:182,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:135,&g&:167,&b&:217,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:222,&g&:243,&b&:248,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:222,&g&:243,&b&:248,&a&:&1&,&position&:100,&align&:&top&}]}"},{21:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:34,&g&:225,&b&:255,&a&:&1&,&position&:0,&align&:&top&},{&r&:34,&g&:225,&b&:255,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:29,&g&:143,&b&:225,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:98,&g&:94,&b&:177,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:98,&g&:94,&b&:177,&a&:&1&,&position&:100,&align&:&top&}]}"},{22:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:44,&g&:216,&b&:213,&a&:&1&,&position&:0,&align&:&top&},{&r&:44,&g&:216,&b&:213,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:107,&g&:141,&b&:214,&a&:&1&,&position&:50,&align&:&bottom&},{&r&:142,&g&:55,&b&:215,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:142,&g&:55,&b&:215,&a&:&1&,&position&:100,&align&:&top&}]}"},{23:"{&type&:&linear&,&angle&:&160&,&colors&:[{&r&:44,&g&:216,&b&:213,&a&:&1&,&position&:0,&align&:&top&},{&r&:44,&g&:216,&b&:213,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:197,&g&:193,&b&:255,&a&:&1&,&position&:56,&align&:&bottom&},{&r&:255,&g&:186,&b&:195,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:186,&b&:195,&a&:&1&,&position&:100,&align&:&top&}]}"},{24:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:191,&g&:217,&b&:254,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:191,&g&:217,&b&:254,&a&:&1&,&position&:0,&align&:&top&},{&r&:223,&g&:137,&b&:181,&a&:&1&,&position&:100,&align&:&top&},{&r&:223,&g&:137,&b&:181,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{25:"{&type&:&linear&,&angle&:&340&,&colors&:[{&r&:97,&g&:97,&b&:97,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:97,&g&:97,&b&:97,&a&:&1&,&position&:0,&align&:&top&},{&r&:155,&g&:197,&b&:195,&a&:&1&,&position&:100,&align&:&top&},{&r&:155,&g&:197,&b&:195,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{26:"{&type&:&linear&,&angle&:&90&,&colors&:[{&r&:36,&g&:57,&b&:73,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:36,&g&:57,&b&:73,&a&:&1&,&position&:0,&align&:&top&},{&r&:81,&g&:127,&b&:164,&a&:&1&,&position&:100,&align&:&top&},{&r&:81,&g&:127,&b&:164,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{27:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:234,&g&:205,&b&:163,&a&:&1&,&position&:0,&align&:&top&},{&r&:234,&g&:205,&b&:163,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:230,&g&:185,&b&:128,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:230,&g&:185,&b&:128,&a&:&1&,&position&:100,&align&:&top&}]}"},{28:"{&type&:&linear&,&angle&:&45&,&colors&:[{&r&:238,&g&:156,&b&:167,&a&:&1&,&position&:0,&align&:&top&},{&r&:238,&g&:156,&b&:167,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:255,&g&:221,&b&:225,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:255,&g&:221,&b&:225,&a&:&1&,&position&:100,&align&:&top&}]}"},{29:"{&type&:&linear&,&angle&:&340&,&colors&:[{&r&:247,&g&:148,&b&:164,&a&:&1&,&position&:0,&align&:&top&},{&r&:247,&g&:148,&b&:164,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:253,&g&:214,&b&:189,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:253,&g&:214,&b&:189,&a&:&1&,&position&:100,&align&:&top&}]}"},{30:"{&type&:&linear&,&angle&:&45&,&colors&:[{&r&:135,&g&:77,&b&:162,&a&:&1&,&position&:0,&align&:&top&},{&r&:135,&g&:77,&b&:162,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:196,&g&:58,&b&:48,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:196,&g&:58,&b&:48,&a&:&1&,&position&:100,&align&:&top&}]}"},{31:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:243,&g&:231,&b&:233,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:243,&g&:231,&b&:233,&a&:&1&,&position&:0,&align&:&top&},{&r&:218,&g&:212,&b&:236,&a&:&1&,&position&:100,&align&:&top&},{&r&:218,&g&:212,&b&:236,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{32:"{&type&:&linear&,&angle&:&320&,&colors&:[{&r&:43,&g&:88,&b&:118,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:43,&g&:88,&b&:118,&a&:&1&,&position&:0,&align&:&top&},{&r&:78,&g&:67,&b&:118,&a&:&1&,&position&:100,&align&:&top&},{&r&:78,&g&:67,&b&:118,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{33:"{&type&:&linear&,&angle&:&60&,&colors&:[{&r&:41,&g&:50,&b&:60,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:41,&g&:50,&b&:60,&a&:&1&,&position&:0,&align&:&top&},{&r&:72,&g&:85,&b&:99,&a&:&1&,&position&:100,&align&:&top&},{&r&:72,&g&:85,&b&:99,&a&:&1&,&position&:100,&align&:&bottom&}]}"},{34:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:233,&g&:233,&b&:231,&a&:&1&,&position&:0,&align&:&top&},{&r&:233,&g&:233,&b&:231,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:239,&g&:238,&b&:236,&a&:&1&,&position&:25,&align&:&bottom&},{&r&:238,&g&:238,&b&:238,&a&:&1&,&position&:70,&align&:&bottom&},{&r&:213,&g&:212,&b&:208,&a&:&1&,&position&:100,&align&:&bottom&},{&r&:213,&g&:212,&b&:208,&a&:&1&,&position&:100,&align&:&top&}]}"},{35:"{&type&:&linear&,&angle&:&180&,&colors&:[{&r&:251,&g&:200,&b&:212,&a&:&1&,&position&:0,&align&:&bottom&},{&r&:251,&g&:200,&b&:212,&a&:&1&,&position&:0,&align&:&top&},{&r&:151,&g&:149,&b&:240,&a&:&1&,&position&:100,&align&:&top&},{&r&:151,&g&:149,&b&:240,&a&:&1&,&position&:100,&align&:&bottom&}]}"}];function $e(){this.innerHTML=ne[We(this,"data-text")]}function Je(){this.setAttribute("placeholder",ne[We(this,"data-placeholder")])}function Ye(){this.setAttribute("data-message",ne[We(this,"data-alert")])}function We(e,r){return e.getAttribute(r)||""}function Xe(r){r||(r={}),"string"==typeof r&&(r=JSON.parse(r.replace(/\&/g,'"'))),ne=e.extend({},Be,r),L=ne.color,d.find("*[data-placeholder]").each(Je),d.find("*[data-alert]").each(Ye),d.find("*[data-text]").each($e)}function Qe(r,a,t,i){var o,c,n,s,l;if(e.isPlainObject(r))for(var p in r)r.hasOwnProperty(p)&&("string"==typeof(r=r[p])?("gradient"===(r=RevColor.process(r))[1]&&(s=(c=r[2]).angle,l=c.type),r=r[0]):(s=r.angle,l=r.type),o=isNaN(p)?p.replace(/_/g," ").replace(/\b\w/g,function(e){return e.toUpperCase()}):"radial"!==l?s+"&deg;":"radial");else o=r;if("blank"!==r){e.isPlainObject(r)&&(c=r,"",r=i||RevColor.processGradient(r));var v='<span class="rev-cpicker-color tptip'+t+'" data-title="'+o+'" data-color="'+r+'"><span class="rev-cpicker-preset-tile"></span><span class="rev-cpicker-preset-bg" style="background: '+r+'"></span>';return a||(v+='<span class="rev-cpicker-delete"><span class="rev-cpicker-delete-icon"></span></span>'),n=e(v+="</span>"),c&&n.data("gradient",c),n[0]}return(n=document.createElement("span")).className="rev-cpicker-color blank",n}function Ue(){var r=We(this,"data-color").toLowerCase(),a=!Ce&&r===ue.toLowerCase();if(r===pe||a){var t=e(this);return t.closest(".rev-cpicker-presets-group").find(".rev-cpicker-color.selected").removeClass("selected"),_e=t,Ce&&!J&&er(_e.data("gradient"),!0),_e.addClass("selected"),!1}}function Ze(e,r){for(var a=document.createDocumentFragment(),t=-1!==e.search("core"),i=t?"":" rev-picker-color-custom",o=r.length,c=-1!==e.search("colors")?Ee:Te,n=Math.max(Math.ceil(o/Oe),c),s=0;s<n;s++)for(;r.length<(s+1)*Oe;)r[r.length]="blank";for(o=r.length,s=0;s<o;s++)a.appendChild(Qe(r[s],t,i));return["rev-cpicker-"+e,a]}function Ke(e,a,t){if(me){if(!e){var i=a||w.val(),o=void 0!==t?t:be.val();a="transparent"===i?"transparent":"100%"===o?RevColor.sanitizeHex(i):RevColor.processRgba(i,o)}var c="transparent"===a,n=c?"":a;e?k.data("state",a):y.data("state",a),c?me.css("background",n):me[0].style.background=n,l&&l(ve,a),r.trigger("revcolorpickerupdate",[ve,a])}}function er(r,t){var i=RevColor.process(r),c=i[1],n=i[0];if(o&&b.removeClass("checked"),"gradient"!==c){switch(c){case"hex":r=RevColor.sanitizeHex(n),be.val("100%"),Ir(100);break;case"rgba":var s=RevColor.rgbaToHex(n),l=parseInt(100*s[1],10);r=s[0],be.val(l+"%"),Ir(l);break;case"rgb":r=RevColor.rgbToHex(n),be.val("100%"),Ir(100);break;default:Q.click(),y.click()}D.val(r).change(),t||y.click()}else o?(!function(r){var t=r.angle;"radial"===r.type&&(t="radial");U.removeClass("selected"),e('.rev-cpicker-orientation[data-direction="'+t+'"]').addClass("selected"),a.val(cr(t)),Cr(t),function(e){for(var r,a,t=document.createDocumentFragment(),i=e.length,o=0;o<i;o++)a=e[o],r=a.align,t.appendChild(or(r,a.position,ir(a),tr(a,r)));te&&te.draggable("destroy");ie.empty().append(t),te=ie.children().draggable(Le)}(r.colors)}(i[2]),vr(),t||(ee=!0,k.click())):(D.val(RevColor.defaultValue).change(),y.click());return[n,c]}function rr(r,a){var t=function(e,r){var a,t=c.slice(),i=t.length;for(t.sort(mr);i--;)if((a=t[i]).align===e&&a.x<r)return a;i=t.length;for(var o=0;o<i;o++)if((a=t[o]).align===e&&a.x>r)return a}(r,a).color,i=tr(t,r,!0),o=or(r,a,ir(t,!0),i);N&&N.removeClass("active"),N=e(o).addClass("active").appendTo(ie).draggable(Le),P=N.children(".rev-cpicker-point-square")[0],z=N.children(".rev-cpicker-point-triangle")[0],te=ie.children();var n=ar(a);vr(o),"bottom"===r&&_.val(n[1]).change()}function ar(e){void 0===e&&(e=c[Z].x);var r=N.attr("data-color"),a=N.hasClass("rev-cpicker-point-bottom");if(a)Re.hasClass("active")&&(ae.attr("disabled","disabled"),K.attr("disabled","disabled"),Re.removeClass("active")),r=RevColor.rgbaToHex(r)[0],x.css("background",r),R.removeAttr("disabled").val(e+"%"),d.find(".rev-cpicker-point-bottom").length>2&&ce.addClass("active"),d.addClass("open");else{ce.hasClass("active")&&(x.css("background",""),R.attr("disabled","disabled"),ce.removeClass("active"));var t=RevColor.getOpacity(r);ae.attr("data-opacity",t).val(t).removeAttr("disabled"),K.val(e+"%").removeAttr("disabled"),d.find(".rev-cpicker-point-top").length>2&&Re.addClass("active"),d.removeClass("open")}return[a,r]}function tr(e,r,a){return"bottom"===r?"rgb("+e.r+","+e.g+","+e.b+")":"rgba(0, 0, 0, "+(a?"1":e.a)+")"}function ir(e,r){var a=r?"1":e.a;return"rgba("+e.r+","+e.g+","+e.b+","+a+")"}function or(r,a,t,i){var o=document.createElement("span");return o.className="rev-cpicker-point rev-cpicker-point-"+r,"string"==typeof t?o.setAttribute("data-color",t):e(o).data("gradient",t),o.setAttribute("data-location",a),o.style.left=a+"%",o.innerHTML="bottom"===r?'<span class="rev-cpicker-point-triangle" style="border-bottom-color: '+i+'"></span><span class="rev-cpicker-point-square" style="background: '+i+'"></span>':'<span class="rev-cpicker-point-square" style="background: '+i+'"></span><span class="rev-cpicker-point-triangle" style="border-top-color: '+i+'"></span>',o}function cr(e){return e&&"radial"!==e||(e="0"),O.innerHTML=e+"&deg;",O.value}function nr(){N&&(N.removeClass("active"),N=!1),R.attr("disabled","disabled"),ae.attr("disabled","disabled"),K.attr("disabled","disabled"),Re.removeClass("active"),ce.removeClass("active"),x.css("background",""),d.removeClass("open")}function sr(e,a){d.removeClass("active is-basic").hide(),i.removeClass("rev-colorpicker-open"),we.css({left:"",top:""}),F&&(F.remove(),F=!1),_e?(_e.hasClass("selected")?(a&&ve.data("hex",_e.attr("data-color").toLowerCase()),_e.removeClass("selected")):ve.removeData("hex"),_e=!1):ve.removeData("hex"),a||(j&&j(),ke&&"transparent"!==ke?me[0].style.background=ke:me.css("background",""),r.trigger("revcolorpickerupdate",[ve,ke])),me=!1,ve=!1}function lr(){var r=e(this).children(".rev-cpicker-color").not(".blank").length;return r>Oe?e("#"+this.id+"-btn").addClass("multiplerows"):e("#"+this.id+"-btn").removeClass("multiplerows"),r}function pr(){if(-1===this.className.search("blank"))return I=!1,!1}function vr(e,r,t){G=r,fr(),G=!1;for(var i,o,s,l=[],p=c.length,v=0;v<p;v++)o=(s=c[v]).color,l[v]=o,(i=s.el).setAttribute("data-color",RevColor.rgbaString(o.r,o.g,o.b,o.a)),i.setAttribute("data-opacity",100*o.a),e&&e===i&&(Z=v);n.hasClass("selected")?(Ae.type="radial",Ae.angle="0"):(Ae.type="linear",Ae.angle=parseInt(a.val(),10).toString()),Ae.colors=l,_e&&_e.removeClass("selected");var d=RevColor.processGradient(Ae);if(Ke(!0,d),t)return[Ae,d];B.style.background=function(e,r){var a=(e=e.split("("))[0];e.shift();var t=e.join("(").split(",");return t.shift(),a+"("+(r=void 0!==r?r+"deg,":"ellipse at center,")+t.join(",")}(d.replace("radial-","linear-"),"90"),X.style.background=d}function dr(e,r){if(0===e)return!1;for(var a;e--;)if((a=c[e]).align!==r)return a;return!1}function gr(e,r,a){if(e===a)return!1;for(var t;e++<a;)if((t=c[e]).align!==r)return t;return!1}function br(e,r,a,t,i){return Math.max(Math.min(Math.round(Math.abs((e-r)/(a-r)*(i-t)+t)),255),0)}function kr(e,r,a){var t,i,o,c,n,s,l=r.alpha,p=a.alpha;t=l!==p?(i=e.x,o=r.x,c=a.x,n=l,s=p,Math.max(Math.min(Math.abs(parseFloat(((i-o)/(c-o)*(s-n)).toFixed(2))+parseFloat(n)),1),0)).toFixed(2):l,e.alpha=t,e.color.a=t}function ur(e,r,a){var t=e.color,i=r.color,o=a.color;if(r!==a){var c=e.x,n=r.x,s=a.x;t.r=br(c,n,s,i.r,o.r),t.g=br(c,n,s,i.g,o.g),t.b=br(c,n,s,i.b,o.b)}else t.r=i.r,t.g=i.g,t.b=i.b}function fr(){c=[],V=[],T=[],te.each(hr),c.sort(mr);for(var e,r,a,t,i=c.length,o=i-1,n=0;n<i;n++)!1===(r=dr(n,a=(e=c[n]).align))&&(r=gr(n,a,o)),!1===(t=gr(n,a,o))&&(t=dr(n,a)),"bottom"===a?kr(e,r,t):ur(e,r,t);c.sort(mr)}function mr(e,r){return e.x<r.x?-1:e.x>r.x?1:0}function hr(e){var r=RevColor.rgbValues(We(this,"data-color"),4),a=-1!==this.className.search("bottom")?"bottom":"top",t=r[3].replace(/\.?0*$/,"")||0,i=parseInt(this.style.left,10);G&&(i<50?i+=2*(50-i):i-=2*(i-50),this.style.left=i+"%",this.setAttribute("data-location",i)),c[e]={el:this,x:i,alpha:t,align:a,color:{r:parseInt(r[0],10),g:parseInt(r[1],10),b:parseInt(r[2],10),a:t,position:i,align:a}},N&&N[0]!==this&&("bottom"===a?T[T.length]=i:V[V.length]=i)}function Cr(e){e=void 0!==e?e:parseInt(a.val(),10),W[0].style.transform="rotate("+e+"deg)"}function xr(r,t,i){var o,c,n=void 0!==i,s=n?i:parseInt(a.val(),10);if(r&&"keyup"===r.type)o=!isNaN(s)&&s>=-360&&s<=360,c=s;else{var l=parseInt(a.data("orig-value"),10);s||(s="0"),(isNaN(s)||s<-360||s>360)&&(s=l),s!==l&&(c=s,o=!0,a.val(cr(s)),n||(s=t||s,U.removeClass("selected"),e('.rev-cpicker-orientation[data-direction="'+s+'"]').addClass("selected")))}(o||t)&&(c&&Cr(c),vr())}function yr(){var r=e(this);-1!==this.className.search("down")?(r.parent().addClass("active"),r.closest(".rev-cpicker-presets").addClass("active"),e(this.id.replace("-btn","")).addClass("active"),xe=d.hasClass("gradient-view")):(r.parent().removeClass("active"),r.closest(".rev-cpicker-presets").removeClass("active"),e(this.id.replace("-btn","")).removeClass("active"),xe=!1)}function wr(e,r){var a=parseInt(100*(Math.round(r.position.left)/(He-2)).toFixed(2),10);"bottom"===S?R.val(a+"%").trigger("keyup"):K.val(a+"%").trigger("keyup")}function Rr(){var r=e(this);S=r.hasClass("rev-cpicker-point-bottom")?"bottom":"top",r.click()}function _r(){"bottom"===S?R.trigger("focusout"):K.trigger("focusout")}function Ir(e){Ne=!0,Ie.slider("value",Math.round(.01*e*De)),Ne=!1}function Nr(e){var r=Y.offset(),a=e.pageX-r.left,t=e.pageY-r.top;if(!isNaN(a)&&!isNaN(t)){var i=Math.atan2(t-Ve,a-Ve)*(180/Math.PI)+90;i<0&&(i+=360),i=Math.max(0,Math.min(360,Math.round(i))),i=5*Math.round(i/5),!0,xr(!1,!1,i),!1}}function Fr(e){e.stopImmediatePropagation()}function Mr(){g||e.tpColorPicker(),Fe=document.getElementById("rev-cpicker-current-edit"),X=document.getElementById("rev-cpicker-gradient-output"),B=document.getElementById("rev-cpicker-gradient-input"),$=document.getElementById("rev-cpicker-edit-title"),O=document.createElement("textarea"),Re=e("#rev-cpicker-opacity-delete"),ie=e("#rev-cpciker-point-container"),K=e("#rev-cpicker-opacity-location"),de=e(".rev-cpicker-presets-group"),be=e("#rev-cpicker-color-opacity"),n=e("#rev-cpicker-orientation-radial"),ce=e("#rev-cpicker-color-delete"),ae=e("#rev-cpicker-grad-opacity"),R=e("#rev-cpicker-color-location"),M=e("#rev-cpicker-gradients-core"),U=e(".rev-cpicker-orientation"),_=e("#rev-cpicker-iris-gradient"),W=e("#rev-cpicker-wheel-point"),oe=e("#rev-cpicker-gradients"),D=e("#rev-cpicker-iris-color"),k=e("#rev-cpicker-gradient-btn"),u=e("#rev-cpicker-gradient-hex"),Q=e("#rev-cpciker-clear-hex"),b=e("#rev-cpicker-meta-reverse"),E=e("#rev-cpicker-hit-bottom"),Ie=e("#rev-cpicker-scroll"),ge=e("#rev-cpicker-colors"),w=e("#rev-cpicker-color-hex"),y=e("#rev-cpicker-color-btn"),x=e("#rev-cpicker-color-box"),a=e("#rev-cpicker-meta-angle"),Y=e("#rev-cpicker-wheel"),s=e("#rev-cpicker-hit-top"),we=e("#rev-cpicker"),r=e(document),Le.drag=wr,Le.stop=_r,Le.start=Rr,y.data("state",ge.find(".rev-cpicker-color").eq(0).attr("data-color")||"#ffffff"),k.data("state",oe.find(".rev-cpicker-color").eq(0).attr("data-color")||"linear-gradient(0deg, rgba(255, 255, 255, 1) 0%, rgba(0, 0, 0, 1) 100%)"),we.draggable({containment:"window",handle:".rev-cpicker-draggable",stop:function(){we.css("height","auto")}}),de.perfectScrollbar({wheelPropagation:!1,suppressScrollX:!0}),Y.on("mousedown.revcpicker",function(e){U.removeClass("selected"),q=!0,Nr(e)}).on("mousemove.revcpicker",function(e){q&&Nr(e)}).on("mouseleave.revcpicker mouseup.revcpicker",function(){q=!1}),e(".rev-cpicker-main-btn").on("click.revcpicker",function(){var a;if(J=-1===this.id.search("gradient"),me&&(a=e(this).data("state")),J?(me&&(pe=w.val()),d.removeClass("gradient-view").addClass("color-view")):(me&&(pe=a),d.removeClass("color-view").addClass("gradient-view"),ee||M.children(".rev-cpicker-color").not(".blank").eq(0).click()),de.perfectScrollbar("update"),a){var t="transparent"===a,i=t?"":a;t?me.css("background",i):me[0].style.background=i,Ce=!0,e(".rev-cpicker-color").not(".blank").each(Ue),Ce=!1,r.trigger("revcolorpickerupdate",[ve,a])}}),e("#rev-cpicker-check").on("click.revcipicker",function(){var a,t,i;if(d.hasClass("color-view")){var o=w.val(),c=be.val();ve.removeData("gradient"),t="transparent"===o?"transparent":"100%"===c?RevColor.sanitizeHex(o):RevColor.processRgba(o,c),a=[ve,t,!1]}else{nr();var n=vr(!1,!1,!0),s=e.extend({},n[0]),l=n[1];ve.data("gradient",l),t=JSON.stringify(s).replace(/\"/g,"&"),a=[ve,l,s]}(i=a[1]!==ke)&&(ve.attr("data-color",a[1]).val(t).change(),r.trigger("revcolorpicker",a),je&&je(a[0],a[1],a[2])),sr(0,i)}),d.on("click.revcpicker",function(r){if(d.hasClass("open")){var t=r.target,i=e(t),o=t.id,c=-1!==t.className.search("rev-cpicker-point")||"rev-cpicker-section-right"===o||-1!==o.search("hit")||i.closest("#rev-cpicker-section-right, #rev-cpicker-point-wrap").length;c&&("text"===i.attr("type")?c=!i.attr("disabled"):"rev-cpicker-check-gradient"===o&&(c=!1)),c||nr()}else re&&!1===/wheel|angle|reverse/.test(r.target.id)&&(-1===r.target.id.search("radial")&&e('.rev-cpicker-orientation[data-direction="'+parseInt(a.val())+'"]').addClass("selected"),Y.removeClass("active"),re=!1)}),e(".rev-cpicker-close").on("click.revcpicker",sr),D.wpColorPicker({palettes:!1,width:267,border:!1,hide:!1,change:function(e,r){var a=r.color.toString();if(this.value=a,w.val(a),!he){var t=be.val();0===parseInt(t,10)&&(a="transparent"),Ke(!1,a,t),_e&&(_e.removeClass("selected"),_e=!1)}}}),_.wpColorPicker({palettes:!1,height:250,border:!1,hide:!1,change:function(e,r){var a=r.color.toString();this.value=a,u.val(a),x.css("background",a),P.style.backgroundColor=a,z.style.borderBottomColor=a;var t=RevColor.processRgba(a,100),i=RevColor.rgbValues(t,4),o=Ae.colors[Z];o.r=i[0],o.g=i[1],o.b=i[2],o.a=i[3],N.attr("data-color",t),vr()}}),Ie.slider({orientation:"vertical",max:De,value:De,start:function(){ye="transparent"===w.val()},slide:function(e,r){if(!Ne){var a,t=parseInt(100*(r.value/De).toFixed(2),10);ye&&(a=t?"#ffffff":"transparent",w.val(a)),0===t&&(a="transparent"),Ke(!1,a,t||"transparent"),be.val(t+"%")}}}),e(".rev-cpicker-point-location").on("keyup.revcpicker focusout.revcpicker",function(e){if(N){var r,a=N.hasClass("rev-cpicker-point-bottom")?"bottom":"top",t="bottom"===a?T:V,i="bottom"===a?R:K,o=i.val().replace("%","")||"0",c=e.type;for(isNaN(o)&&(o="keyup"===c?"0":N.attr("data-location")),r=(o=Math.max(0,Math.min(100,parseInt(o,10))))<50?1:-1;-1!==t.indexOf(o);)o+=r;"focusout"===c&&(i.val(o+"%"),N.attr("data-location",o)),N.css("left",o+"%"),vr()}}).on("focusin.revcpicker",Fr),e("body").on("click.revcpicker",".rev-cpicker-point",function(){ie.find(".rev-cpicker-point.active").removeClass("active"),N=e(this).addClass("active"),P=N.children(".rev-cpicker-point-square")[0],z=N.children(".rev-cpicker-point-triangle")[0],vr(this),_e=!1;var r=ar();r[0]&&_.val(r[1]).change()}).on("mousedown.revcpicker",".rev-cpicker-point",function(r){N=e(this).data("mousestart",r.pageY)}).on("mousemove.revcpicker",function(e){if(N&&N.data("mousestart")){var r=N.data("mousestart"),a=e.pageY;N.hasClass("rev-cpicker-point-bottom")?a>r&&a-r>ze&&ce.hasClass("active")?N.addClass("warning"):N.removeClass("warning"):r>a&&r-a>ze&&Re.hasClass("active")?N.addClass("warning"):N.removeClass("warning")}}).on("mouseup.revcpicker",function(e){if(N&&N.data("mousestart")){var r=N.data("mousestart"),a=e.pageY;N.removeData("mousestart"),N.hasClass("rev-cpicker-point-bottom")?a>r&&a-r>Pe&&ce.hasClass("active")?ce.click():N.removeClass("warning"):r>a&&r-a>Pe&&Re.hasClass("active")?Re.click():N.removeClass("warning")}}).on("change.revcpicker",".rev-cpicker-component",function(){var r=e(this),a=r.data("gradient")||r.val()||"transparent";("transparent"===a||RevColor.transparentRgba(a))&&(a=""),r.data("tpcp").css("background",a)}).on("keypress.revcpicker",function(e){if(d.hasClass("active")){var r=e.which;27==r?sr():13==r&&m&&m.blur()}}).on("click.revcpicker",".rev-cpicker-color:not(.blank)",function(){if(_e){if(_e[0]===this&&_e.hasClass("selected"))return;_e.removeClass("selected")}var r=(_e=e(this)).parent()[0].id,a=-1!==r.search("core")?"core":"custom",t=-1!==r.search("colors")?"colors":"gradients",i=e("#rev-cpicker-"+t+"-"+a+"-btn");if(i.hasClass("active")&&i.find(".rev-cpicker-arrow-up").click(),d.hasClass("color-view")){var o=_e.attr("data-color");he=!0,D.val(o).change(),"transparent"===w.val()&&w.val(o.toLowerCase()),he=!1;var c=be.val();0===parseInt(c,10)&&(o="transparent"),Ke(!1,o,c)}else s.removeClass("full"),E.removeClass("full"),er(_e.data("gradient"),!0),b.removeClass("checked"),M.find(".rev-cpicker-color.selected").removeClass("selected");_e.addClass("selected")}).on("mouseover.revcpicker",".rev-cpicker-color:not(.blank)",function(){xe&&(X.style.background=We(this,"data-color"))}).on("mouseout.revcpicker",".rev-cpicker-color:not(.blank)",function(){xe&&vr()}).on("click.revcpicker",".rev-cpicker-delete",function(){if(v){if(window.confirm(document.getElementById("rev-cpicker-remove-delete").innerHTML)){d.addClass("onajax onajaxdelete");var a=e(this),t=a.parent(),i=t.attr("data-title")||"";if(!i)return void console.log("Preset does not have a name/title");var o=a.closest(".rev-cpicker-presets-group")[0].id,c=-1!==o.search("colors")?"colors":"gradients";r.off("revcpicker_onajax_delete.revcpicker").on("revcpicker_onajax_delete.revcpicker",function(r,i){i&&console.log(i);var c=a.closest(".rev-cpicker-presets-group"),n=c.find(".ps-scrollbar-x-rail"),s=e("#"+o+"-btn");t.remove(),!function(){var r,a=e(this),t=-1!==this.id.search("colors")?Ee:Te,i=a.children(".rev-cpicker-color"),o=i.length,c=Math.ceil(o/Oe),n=t*Oe;o+=1;for(var s=0;s<c;s++){var l=s*Oe,p=i.slice(l,parseInt(l+Oe,10)-1);I=!0,p.each(pr),I&&(o-=Oe)>=n&&(p.remove(),r=!0)}return r}.call(c[0])?e('<span class="rev-cpicker-color blank"></span>').insertBefore(n):c.perfectScrollbar("update"),lr.call(c[0])<Oe+1&&(e('<span class="rev-cpicker-color blank"></span>').insertBefore(n),s.hasClass("active")&&s.children(".rev-cpicker-arrow-up").click()),d.removeClass("onajax onajaxdelete")}),i=e.trim(i.replace(/\W+/g,"_")).replace(/^\_|\_$/g,"").toLowerCase(),v("delete",i,c,"revcpicker_onajax_delete",ve)}return!1}console.log("Ajax callback not defined")}),e(".rev-cpicker-save-preset-btn").on("click.revcpicker",function(){if(v){var a,t,i=e(this),o=i.closest(".rev-cpicker-presets-save-as").find(".rev-cpicker-preset-save").val();if(o&&isNaN(o)){if(a=d.hasClass("color-view")?"colors":"gradients",o=e.trim(o.replace(/\W+/g,"_")).replace(/^\_|\_$/g,"").toLowerCase(),e("#rev-cpicker-"+a+"-custom").find(".rev-cpicker-color").not(".blank").each(function(){if(e.trim(We(this,"data-title").replace(/\W+/g,"_")).replace(/^\_|\_$/g,"").toLowerCase()===o)return alert(i.attr("data-message")),t=!0,!1}),!t){d.addClass("onajax onajaxsave");var c,n,s={};if("colors"===a){var l=w.val(),p=be.val();c="transparent"===l?"transparent":"100%"===p?RevColor.sanitizeHex(l):RevColor.processRgba(l,p)}else n=X.style.background,c=e.extend({},vr(!1,!1,!0)[0]);s[o]=c,r.off("revcpicker_onajax_save.revcpicker").on("revcpicker_onajax_save.revcpicker",function(r,t){if(t)return d.removeClass("onajax onajaxsave"),void alert(i.attr("data-message"));var o=e(Qe(s,!1," rev-picker-color-custom",n)),c=e("#rev-cpicker-"+a+"-custom"),l=c.find(".rev-cpicker-color.blank"),p=e("#"+c[0].id+"-btn");l.length?o.insertBefore(l.eq(0)):o.insertBefore(c.find(".ps-scrollbar-x-rail")),e("#rev-cpicker-"+a+"-custom-btn").click(),lr.call(c[0])>6&&(l.length&&l.last().remove(),p.addClass("active").children(".rev-cpicker-arrow-down").click(),c.perfectScrollbar("update")),o.click(),d.removeClass("onajax onajaxsave")}),v("save",s,a,"revcpicker_onajax_save",ve)}}else alert(i.attr("data-message"))}else console.log("Ajax callback not defined")}),e(".rev-cpicker-preset-title").on("click.revcpicker",function(){var r=e(this),a=r.parent(),t=r.hasClass("active")?"down":"up";yr.call(r.find(".rev-cpicker-arrow-"+t)[0]),a.find(".rev-cpicker-preset-title").removeClass("selected"),r.addClass("selected"),a.find(".rev-cpicker-presets-group").hide(),document.getElementById(this.id.replace("-btn","")).style.display="block",de.perfectScrollbar("update")}),Q.on("click.revcpicker",function(){be.val("0%"),Ir(0),D.val(RevColor.defaultValue).change(),w.val("transparent"),Ke(!1,"transparent")}),d.find('input[type="text"]').on("focusin.revcpicker",function(){m=this}).on("focusout.revcpicker",function(){m=!1}),e(".rev-cpicker-input").on("focusin.revcpicker",function(){var r=e(this);r.data("orig-value",r.val())}),e(".rev-cpicker-hex").on("focusout.revcpicker",function(){var r,a;if("rev-cpicker-color-hex"===this.id){if(a=w.val())if(a=RevColor.sanitizeHex(a),RevColor.isColor.test(a))w.val(a);else{if(!(r=e(this).data("orig-value")))return void Q.click();a=r,w.val(a)}else a="transparent";D.val(a).change()}else a=u.val()||RevColor.defaultValue,a=RevColor.sanitizeHex(a),RevColor.isColor.test(a)||(a=(r=e(this).data("orig-value"))||RevColor.defaultValue),u.val(a),_.val(a).change()}).on("focusin.revcpicker",Fr),e("#rev-cpciker-clear-gradient").on("click.revcpicker",function(){_.val(RevColor.defaultValue).change()}),a.on("keyup.revcpicker focusout.revcpicker",xr).on("focusin.revcpicker",function(){re=!0,Y.addClass("active")}).on("focusin.revcpicker",Fr),U.on("click.revcpicker",function(){var r=e(this),t=r.attr("data-direction");U.removeClass("selected"),r.addClass("selected"),"radial"!==t?a.removeAttr("disabled").val(cr(t)):a.attr("disabled","disabled"),xr(!1,t)}),e(".rev-cpicker-point-delete").on("click.revcpicker",function(){if(-1!==this.className.search("active")){var e=N.hasClass("rev-cpicker-point-bottom")?"bottom":"top",r=d.find(".rev-cpicker-point-"+e).length;r>2&&(N.draggable("destroy").remove(),te=ie.children(),d.click(),vr()),r<=Se&&("bottom"===e?E.removeClass("full"):s.removeClass("full"))}}),e(".rev-cpicker-preset-save").on("focusin.revcpicker",Fr),e(".rev-cpicker-opacity-input").on("keyup.revcpicker focusout.revcpicker",function(r){var a,t=-1===this.id.search("grad"),i=t?be:ae,o=i.val().replace("%",""),c=r.type;if(isNaN(o)&&(o="keyup"===c?"0":e(this).data("orig-value")),o=Math.max(0,Math.min(100,o)),"focusout"===c&&(i.val(o+"%"),t||N.attr("data-opacity",o)),t){Ke(!1,0===parseInt(o,10)&&"transparent",o),Ir(o)}else{var n=RevColor.rgbValues(N.attr("data-color"),3),s=Ae.colors[Z];o=(.01*parseInt(o,10)).toFixed(2).replace(/\.?0*$/,""),s.r=n[0],s.g=n[1],s.b=n[2],s.a=o,a=RevColor.rgbaString(s.r,s.g,s.b,o),N.attr("data-color",a),vr(),a="rgba(0, 0, 0, "+o+")",P.style.backgroundColor=a,z.style.borderTopColor=a}}).on("focusin.revcpicker",Fr),e(".rev-cpicker-builder-hit").on("click.revcpicker",function(e){c||fr();for(var r=parseInt(100*((e.pageX-s.offset().left)/He).toFixed(2),10),a=-1!==this.id.search("bottom")?"bottom":"top",t="bottom"===a?T:V,i=r<50?1:-1;-1!==t.indexOf(r);)r+=i;"bottom"===a?d.find(".rev-cpicker-point-bottom").length<Se?(rr(a,r),_e=!1):E.addClass("full"):d.find(".rev-cpicker-point-top").length<Se?(rr(a,r),_e=!1):s.addClass("full")}),b.on("click.revcpicker",function(){!b.hasClass("checked")?b.addClass("checked"):b.removeClass("checked"),vr(!1,!0)}),e(".rev-cpicker-arrow").on("click.revcpicker",yr),t=!0}function jr(r){var a,t,i,o,c,n=e.extend({},r),s=n.core||{},l=n.custom;!se||l?(o=4,l=(se=l)||{colors:[],gradients:[]}):o=2,s.colors||(s.colors=Ge),s.gradients||(s.gradients=qe);for(var p=0;p<o;p++){switch(p){case 0:a="colors-core",i=s.colors;break;case 1:a="gradients-core",i=s.gradients;break;case 2:a="colors-custom",i=l.colors;break;case 3:a="gradients-custom",i=l.gradients}t=Ze(a,i.slice()||[]),(c=e("#"+t[0])).find(".rev-cpicker-color").remove(),c.prepend(t[1])}}e.tpColorPicker=function(r){i||(i=e("body"),d=e('<div class="rev-cpicker-wrap color-view"><div id="rev-cpicker-back" class="rev-cpicker-close"></div><div id="rev-cpicker"><div id="rev-cpicker-head"><div id="rev-cpicker-drag" class="rev-cpicker-draggable"></div><span id="rev-cpicker-color-btn" class="rev-cpicker-main-btn" data-text="solid_color"></span><span id="rev-cpicker-gradient-btn" class="rev-cpicker-main-btn" data-text="gradient_color"></span><div id="rev-cpicker-editing" class="rev-cpicker-draggable"><span id="rev-cpicker-edit-title" data-text="currently_editing"></span><span id="rev-cpicker-current-edit"></span></div><span id="rev-cpicker-exit" class="rev-cpicker-close"></span></div><div id="rev-cpicker-section-left" class="rev-cpicker-section"><div id="rev-cpicker-body"><div id="rev-cpicker-colors" class="rev-cpicker-type"><div class="rev-cpicker-column rev-cpicker-column-left">\t<div class="rev-cpicker-column-inner-left"><div class="rev-cpicker-presets"><span id="rev-cpicker-colors-core-btn" class="rev-cpicker-preset-title rev-cpicker-preset-title-core selected"><span data-text="core_presets"></span> <span class="rev-cpicker-arrow rev-cpicker-arrow-down"></span><span class="rev-cpicker-arrow rev-cpicker-arrow-up"></span></span><span id="rev-cpicker-colors-custom-btn" class="rev-cpicker-preset-title rev-cpicker-preset-title-custom"><span data-text="custom_presets"></span> <span class="rev-cpicker-arrow rev-cpicker-arrow-down"></span><span class="rev-cpicker-arrow rev-cpicker-arrow-up"></span></span><div id="rev-cpicker-colors-core" class="rev-cpicker-presets-group"></div><div id="rev-cpicker-colors-custom" class="rev-cpicker-presets-group rev-cpicker-presets-custom"></div></div><div class="rev-cpicker-iris"><input id="rev-cpicker-iris-color" class="rev-cpicker-iris-input" value="#ffffff" /><div id="rev-cpicker-scroller" class="iris-slider iris-strip"><div id="rev-cpicker-scroll-bg"></div><div id="rev-cpicker-scroll" class="iris-slider-offset"></div></div></div></div></div><div class="rev-cpicker-column rev-cpicker-column-right"><div class="rev-cpicker-column-inner-right"><div><span data-text="save_a_new_preset"></span><div class="rev-cpicker-presets-save-as"><input type="text" class="rev-cpicker-preset-save" placeholder="" data-placeholder="enter_a_name" /><span class="rev-cpicker-btn rev-cpicker-save-preset-btn" data-alert="naming_error"><span class="rev-cpicker-save-icon"></span><span class="rev-cpicker-preset-save-text" data-text="save"></span></span></div></div><div class="rev-cpicker-meta"><span data-text="color_hex_value"></span><br><input type="text" id="rev-cpicker-color-hex" class="rev-cpicker-input rev-cpicker-hex" value="#ffffff" /><br><span data-text="opacity" class="rev-cpicker-hideable"></span><br><input type="text" id="rev-cpicker-color-opacity" class="rev-cpicker-input rev-cpicker-opacity-input rev-cpicker-hideable" value="100%" /><span id="rev-cpciker-clear-hex" class="rev-cpicker-btn rev-cpicker-btn-small rev-cpciker-clear rev-cpicker-hideable" data-text="clear"></span></div></div></div></div><div id="rev-cpicker-gradients" class="rev-cpicker-type"><div class="rev-cpicker-column rev-cpicker-column-left">\t<div class="rev-cpicker-column-inner-left"><div class="rev-cpicker-presets"><span id="rev-cpicker-gradients-core-btn" class="rev-cpicker-preset-title rev-cpicker-preset-title-core selected"><span data-text="core_presets"></span> <span class="rev-cpicker-arrow rev-cpicker-arrow-down"></span><span class="rev-cpicker-arrow rev-cpicker-arrow-up"></span></span><span id="rev-cpicker-gradients-custom-btn" class="rev-cpicker-preset-title rev-cpicker-preset-title-custom"><span data-text="custom_presets"></span> <span class="rev-cpicker-arrow rev-cpicker-arrow-down"></span><span class="rev-cpicker-arrow rev-cpicker-arrow-up"></span></span><div id="rev-cpicker-gradients-core" class="rev-cpicker-presets-group"></div><div id="rev-cpicker-gradients-custom" class="rev-cpicker-presets-group rev-cpicker-presets-custom"></div></div><div class="rev-cpicker-gradient-block"><div id="rev-cpicker-gradient-input" class="rev-cpicker-gradient-builder"><span id="rev-cpicker-hit-top" class="rev-cpicker-builder-hit"></span><div id="rev-cpicker-point-wrap"><div id="rev-cpciker-point-container"></div></div><span id="rev-cpicker-hit-bottom" class="rev-cpicker-builder-hit"></span></div><div class="rev-cpicker-meta-row-wrap"><div class="rev-cpicker-meta-row"><div><label data-text="opacity"></label><input type="text" id="rev-cpicker-grad-opacity" class="rev-cpicker-point-input rev-cpicker-opacity-input" value="100%" disabled /></div><div><label data-text="location"></label><input type="text" id="rev-cpicker-opacity-location" class="rev-cpicker-point-input rev-cpicker-point-location" value="100%" disabled /></div><div><label>&nbsp;</label><span class="rev-cpicker-btn rev-cpicker-btn-small rev-cpicker-point-delete" id="rev-cpicker-opacity-delete" data-text="delete">{{delete}}</span></div></div><div class="rev-cpicker-meta-row"><div><label data-text="color"></label><span class="rev-cpicker-point-input" id="rev-cpicker-color-box"></span></div><div><label data-text="location"></label><input type="text" id="rev-cpicker-color-location" class="rev-cpicker-point-input rev-cpicker-point-location" value="100%" disabled /></div><div><label>&nbsp;</label><span class="rev-cpicker-btn rev-cpicker-btn-small rev-cpicker-point-input rev-cpicker-point-delete" id="rev-cpicker-color-delete" data-text="delete">{{delete}}</span></div></div></div></div></div></div><div class="rev-cpicker-column rev-cpicker-column-right"><div class="rev-cpicker-column-inner-right"><div><span data-text="save_a_new_preset"></span><div class="rev-cpicker-presets-save-as"><input type="text" class="rev-cpicker-preset-save" placeholder="" data-placeholder="enter_a_name" /><span class="rev-cpicker-btn rev-cpicker-save-preset-btn" data-alert="naming_error"><span class="rev-cpicker-save-icon"></span><span class="rev-cpicker-preset-save-text" data-text="save"></span></span></div></div><div class="rev-cpicker-gradient-block"><div id="rev-cpicker-gradient-output" class="rev-cpicker-gradient-builder"></div></div><div class="rev-cpicker-meta-row-wrap"><div class="rev-cpicker-meta-row"><div><label>Orientation</label><span id="rev-cpicker-orientation-horizontal" class="rev-cpicker-btn rev-cpicker-btn-small rev-cpicker-orientation" data-direction="90" data-text="horizontal"></span><span id="rev-cpicker-orientation-vertical" class="rev-cpicker-btn rev-cpicker-btn-small rev-cpicker-orientation" data-direction="180" data-text="vertical"></span><span id="rev-cpicker-orientation-radial" class="rev-cpicker-btn rev-cpicker-btn-small rev-cpicker-orientation" data-direction="radial" data-text="radial"></span></div></div><div class="rev-cpicker-meta-row rev-cpicker-meta-row-push"><div><label data-text="enter_angle"></label><div id="rev-cpicker-angle-container"><input type="text" class="rev-cpicker-input" id="rev-cpicker-meta-angle" value="" /><div id="rev-cpicker-wheel"><div id="rev-cpicker-wheel-inner"><span id="rev-cpicker-wheel-point"></span></div></div></div></div><div><label data-text="reverse_gradient"></label><span id="rev-cpicker-meta-reverse"></span></div></div></div></div></div></div></div></div><span id="rev-cpicker-check"></span><div id="rev-cpicker-section-right" class="rev-cpicker-section"><div class="rev-cpicker-iris"><input id="rev-cpicker-iris-gradient" class="rev-cpicker-iris-input" value="#ffffff" /></div><div class="rev-cpicker-fields"><input type="text" id="rev-cpicker-gradient-hex" class="rev-cpicker-input rev-cpicker-hex" value="#ffffff" /><span id="rev-cpciker-clear-gradient" class="rev-cpicker-btn rev-cpicker-btn-small rev-cpciker-clear" data-text="clear"></span><span id="rev-cpicker-check-gradient" class="rev-cpicker-btn"></span></div></div><span id="rev-cpicker-remove-delete" data-text="delete_confirm"></span></div></div>').appendTo(i)),r||(r={}),r.core&&(r.core.colors&&(Ge=r.core.colors),r.core.gradients&&(qe=r.core.gradients)),jr(r),g?(de.perfectScrollbar("update"),r.mode&&(f=r.mode),r.language&&Xe(r.language)):(Xe(r.language||Be),f=r.mode||"full"),r.init&&(p=r.init),r.onAjax&&(C=r.onAjax),r.onEdit&&(h=r.onEdit),r.change&&(H=r.change),r.cancel&&(A=r.cancel),r.widgetId&&(le=r.widgetId),r.defaultValue&&(RevColor.defaultValue=r.defaultValue),r.wrapClasses&&(Me=r.wrapClasses),r.appendedHtml&&(fe=r.appendedHtml),g=!0};var Ar={refresh:function(){var r=e(this);if(r.hasClass("rev-cpicker-component")){var a=r.data("revcp")||{},t=r.val()||a.defaultValue||RevColor.defaultValue,i=RevColor.process(t);t=i[0],3===i.length&&r.val(t),i="rgba"===i[1]&&RevColor.transparentRgba(t,!0)?"":t,"transparent"!==t?r.data("tpcp")[0].style.background=i:r.data("tpcp").css("background",""),r.attr("data-color",t).data("hex",t)}},destroy:function(){e(this).removeData().closest(".rev-cpicker-master-wrap").removeData().remove()}};e.fn.tpColorPicker=function(r){return r&&"string"==typeof r?this.each(Ar[r]):this.each(function(){var a=e(this);if(a.hasClass("rev-cpicker-component"))a.tpColorPicker("refresh");else{var t,i,o,c=e('<span class="rev-colorpicker"></span>').data("revcolorinput",a),n=e('<span class="rev-colorbox" />'),s=e('<span class="rev-colorbtn" />'),l=a.attr("data-wrap-classes"),v=a.attr("data-wrapper"),d=a.attr("data-wrap-id"),g=a.attr("data-title"),b=a.attr("data-skin"),k=a.val()||"",u=!!/(?=.*false)(?=.*rgba)/.test(o=k)&&o.replace("false","1");if(u&&(k=u,a.val(u)),c.insertBefore(a).append([n,s,a]),r&&e.isPlainObject(r)){v||(v=r.wrapper),l||(l=r.wrapClasses),b||(b=r.skin),d||(d=r.wrapId),g||(g=r.title),i=r.defaultValue;var f=a.data("revcp");f&&(r=e.extend({},f,r)),a.data("revcp",r)}l||(l=Me),l&&c.addClass(l),d&&c.attr("id",d),k||(k=i||RevColor.defaultValue,a.val(k)),k=(t=RevColor.process(k))[0],"transparent"!==(t="rgba"===t[1]&&RevColor.transparentRgba(k,!0)?"":k)&&(n[0].style.background=t),s[0].innerHTML=g||L||Be.color,a.attr({type:"hidden","data-color":k}).data("tpcp",n).addClass("rev-cpicker-component"),b&&c.addClass(b),v?(v=e(v).addClass("rev-cpicker-master-wrap"),c.wrap(v)):c.addClass("rev-cpicker-master-wrap");var m=!!r&&(r.init||p);m&&m(c,a,k,r)}})},e(function(){e("body").on("click.revcpicker",".rev-colorpicker",function(){t||Mr();var r,a,c,n,s,p,g,b,u,m,x,w,R,_=(ve=e(this).data("revcolorinput")).attr("data-widget-id"),I=ve.attr("data-appended-html"),N=ve.attr("data-editing"),M=ve.attr("data-colors"),S=ve.attr("data-mode"),V=ve.data("revcp"),T=ve.attr("data-lang");if(M&&((M=JSON.parse(M.replace(/\&/g,'"'))).colors&&(p=M.colors),M.gradients&&(n=M.gradients)),V){var E=V.colors;E&&(E.core&&(c=E.core.colors,r=E.core.gradients),E.custom&&(s=E.custom.colors,a=E.custom.gradients)),x=V.onEdit,w=V.onAjax,b=V.change,u=V.cancel,T||(T=V.lang),S||(S=V.mode),I||(I=V.appendedHtml),N||(N=V.editing),_||(_=V.widgetId)}(r||c||a||s||n||p)&&(g={},(r||c||n||p)&&(g.core={colors:p||c||Ge,gradients:n||r||qe}),(a||s)&&(g.custom={colors:s||Ge,gradients:a||qe}),jr(g)),_||(_=le),_&&(d[0].id=_),I||(I=fe),I&&(F=e(I).appendTo(we)),T&&Xe(T),S||(S=f),N?$.style.visibility="visible":(N="",$.style.visibility="hidden"),Fe.innerHTML=N,"single"===S||"basic"===S?(o=!1,k.hide(),y.show(),"basic"===S&&d.addClass("is-basic")):(o=!0,k.show(),y.show()),(R=ve.val()||ve.attr("data-color")||RevColor.defaultValue).split("||").length>1&&(R=RevColor.joinToRgba(R),ve.val(R)),m=er(R),ke=m[0],l=x||h,v=w||C,j=u||A,je=b||H,"gradient"!==m[1]?y.data("state",ke):k.data("state",ke),i.addClass("rev-colorpicker-open"),me=ve.data("tpcp"),d.data("revcpickerinput",ve).addClass("active").show(),de.each(lr).perfectScrollbar("update"),ue=ve.attr("data-color"),pe=ve.data("hex"),e(".rev-cpicker-color").not(".blank").each(Ue)})})}("undefined"!==e&&e)}($nwd_jQuery);