<?php
require_once __DIR__ . '/../abstract.php';

class PFG_Import_Coupons extends Mage_Shell_Abstract
{
    protected Varien_Db_Adapter_Interface $_writeConnection;

    public function run()
    {
        /* @var $rule Mage_SalesRule_Model_Rule */
        $rule = Mage::getModel('salesrule/rule');
        $id = $this->getArg('rule');
        if($id) {
            $rule->load($id);
        }

        if ($rule->getId() < 1) {
            throw new Exception('Invalid Rule');
        }

        $filePath = realpath($this->getArg('file'));
        $file = fopen($filePath, 'r');
        if (!$file) {
            throw new Exception("Invalid file - '$filePath'");
        }

        $now = $rule->getResource()->formatDate(
            Mage::getSingleton('core/date')->gmtTimestamp()
        );

        /** @var $coupon Mage_SalesRule_Model_Coupon */
        $coupon = Mage::getModel('salesrule/coupon');
        while (($line = fgetcsv($file)) !== FALSE) {
            $code = $line[0];

            if (!$code) {
                echo "Empty code skipped" . PHP_EOL;
            }

            $expirationDate = $rule->getToDate();
            if ($expirationDate instanceof Zend_Date) {
                $expirationDate = $expirationDate->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);
            }

            echo "Creating code: $code" . PHP_EOL;
            $coupon->setId(null)
                ->setRuleId($rule->getId())
                ->setUsageLimit($rule->getUsesPerCoupon())
                ->setUsagePerCustomer($rule->getUsesPerCustomer())
                ->setExpirationDate($expirationDate)
                ->setCreatedAt($now)
                ->setType(Mage_SalesRule_Helper_Coupon::COUPON_TYPE_SPECIFIC_AUTOGENERATED)
                ->setCode($code)
                ->save();
        }

        fclose($file);
    }
}

$shell = new PFG_Apply_CatalogRules();
$shell->run();
