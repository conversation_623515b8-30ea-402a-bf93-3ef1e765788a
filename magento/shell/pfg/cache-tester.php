<?php
require_once __DIR__ . '/../abstract.php';

class CacheTester extends Mage_Shell_Abstract
{
    public function run()
    {
        $cacheKey = $this->getArg('key');
        echo '--------------------------------------------------------'.PHP_EOL;
        echo 'Cache key: ' . $cacheKey . PHP_EOL;
        echo '--------------------------------------------------------'.PHP_EOL;

        if ($cacheKey) {
            var_export(Mage::getSingleton('fpc/fpc')->load($cacheKey));
//            echo $this->_getCache($cacheKey);
        }
    }

    protected function _getCache(string $key): string
    {
        return Mage::app()->getCache()->load($key);
    }
}

$shell = new CacheTester();
$shell->run();
