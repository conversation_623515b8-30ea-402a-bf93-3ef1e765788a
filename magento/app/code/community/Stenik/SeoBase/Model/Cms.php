<?php
/**
 * @package Stenik_SeoBase
 * <AUTHOR> Magento Team <<EMAIL>>
 */
class Stenik_SeoBase_Model_Cms extends Mage_Core_Model_Abstract
{

    public function setCanonical($pageId)
    {
        // Get page identifier
        $page = Mage::getModel('cms/page');
        if (!is_null($pageId) && $pageId !== $page->getId()) {
            $page->setStoreId(Mage::app()->getStore()->getId());
            if (!$page->load($pageId)) {
                return null;
            }
        }

        if (!$page->getId()) {
            return null;
        }

        // Check if page is allowed to have canonical
        $allowedPageIds = Mage::helper('stenik_seobase/cms')->getCmsCanonicalPageIds();
        if (!in_array($page->getIdentifier(), $allowedPageIds)) {
            return;
        }

        $url = Mage::helper('cms/page')->getPageUrl($pageId);

        $headBlock = Mage::app()->getLayout()->getBlock('head');
        if ($headBlock && $url) {
            $headBlock->addLinkRel('canonical', $url);
        }
    }
}
