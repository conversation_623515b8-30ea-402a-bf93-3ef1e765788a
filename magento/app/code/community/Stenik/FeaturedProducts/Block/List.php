<?php
/**
 * @package Stenik_FeaturedProducts
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * Magic methods
 *
 * @method Stenik_FeaturedProducts_Block_List setProductLimit()
 * @method Stenik_FeaturedProducts_Block_List getProductLimit()
 * @method Stenik_FeaturedProducts_Block_List setTitle()
 * @method Stenik_FeaturedProducts_Block_List getTitle()
 *
 * @method Stenik_FeaturedProducts_Block_List setViewMore()
 * @method Stenik_FeaturedProducts_Block_List getViewMore()
 * @method Stenik_FeaturedProducts_Block_List setViewMoreTitle()
 * @method Stenik_FeaturedProducts_Block_List getViewMoreTitle()
 * @method Stenik_FeaturedProducts_Block_List setViewMoreLink()
 * @method Stenik_FeaturedProducts_Block_List getViewMoreLink()
 * @method Stenik_FeaturedProducts_Block_List getCategoryId()
 *
 * Supported filter modes: new/promo/bestsellers
 * @method Stenik_FeaturedProducts_Block_List setMode()
 * @method Stenik_FeaturedProducts_Block_List getMode()
 *
 * Supported orders: rand/newest
 * @method Stenik_FeaturedProducts_Block_List setOrder()
 * @method Stenik_FeaturedProducts_Block_List getOrder()
 *
 */
class Stenik_FeaturedProducts_Block_List extends Stenik_FeaturedProducts_Block_Abstract
{
    /**
     * constructor
     */
    protected function _construct() {

        if(is_null($this->getCategoryId()) || is_null($this->getMode())) {
            $this->setTemplate('');
            return;
        }

        $this->addData(array('cache_lifetime' => 600));

        parent::_construct();
    }

    /**
     * Build product collection
     * @return [type] [description]
     */
    public function getProductCollection() {
        $this->_getProductCollection();

        if($this->getMode()) {
            $this->_filterCollectionByMode($this->_productCollection);
        }

        $this->_addCategoryFilterToCollection($this->_productCollection);

        if($this->getOrder() == 'rand') {
            $this->_productCollection->getSelect()->order(new Zend_Db_Expr('RAND()'));
        }

        if($this->getOrder() == 'newest') {
            $this->_productCollection->addAttributeToSort('entity_id', Varien_Data_Collection::SORT_ORDER_DESC);
        }

        if($this->getOrder() == 'category-sort-order') {
            $this->_productCollection->addAttributeToSort('position', Varien_Data_Collection::SORT_ORDER_ASC);
        }

        if ((int)$this->getProductLimit()) {
            $this->_productCollection->setPageSize((int)$this->getProductLimit());
        }

        return $this->_productCollection;
    }

    /**
     * Filter collection by predefined mode
     * new/newest/promo/bestsellers
     */
    protected function _filterCollectionByMode($productCollection) {

        if ($this->getMode() == 'new') {
            $todayStartOfDayDatetime  = Mage::app()->getLocale()->date()->setTime('00:00:00')->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);
            $todayEndOfDayDatetime  = Mage::app()->getLocale()->date()->setTime('23:59:59')->toString(Varien_Date::DATETIME_INTERNAL_FORMAT);

            if ($productCollection->isEnabledFlat()) {

                $connection = $productCollection->getConnection();

                $newsFromDateColumnAlias = 'stenik_featuredproducts_news_from_date';
                $newsToDateColumnAlias   = 'stenik_featuredproducts_news_to_date';

                $productCollection->joinAttribute($newsFromDateColumnAlias, 'catalog_product/news_from_date', 'entity_id', null, 'left');
                $productCollection->joinAttribute($newsToDateColumnAlias, 'catalog_product/news_to_date', 'entity_id', null, 'left');

                $newsFromDateValue = sprintf("IF (at_%s.value IS NOT NULL, at_%s.value, at_%s_default.value)", $newsFromDateColumnAlias, $newsFromDateColumnAlias, $newsFromDateColumnAlias);
                $newsToDateValue   = sprintf("IF (at_%s.value IS NOT NULL, at_%s.value, at_%s_default.value)", $newsToDateColumnAlias, $newsToDateColumnAlias, $newsToDateColumnAlias);

                $whereConditions = array(
                    $connection->quoteInto("({$newsFromDateValue} <= ?) OR ({$newsFromDateValue} IS NULL)", $todayEndOfDayDatetime),
                    $connection->quoteInto("({$newsToDateValue} >= ?) OR ({$newsToDateValue} IS NULL)", $todayStartOfDayDatetime),
                    $connection->quoteInto("({$newsFromDateValue} IS NOT NULL) OR ({$newsToDateValue} IS NOT NULL)", null),
                );
                $productCollection->getSelect()->where('(' . implode(') AND (', $whereConditions) . ')');
            } else {
                $productCollection
                    ->addAttributeToFilter('news_from_date', array('or'=> array(
                        0 => array('date' => true, 'to' => $todayStartOfDayDatetime),
                        1 => array('is' => new Zend_Db_Expr('null')))
                    ), 'left')
                    ->addAttributeToFilter('news_to_date', array('or'=> array(
                        0 => array('date' => true, 'from' => $todayEndOfDayDatetime),
                        1 => array('is' => new Zend_Db_Expr('null')))
                    ), 'left')
                    ->addAttributeToFilter(array(
                        array('attribute' => 'news_from_date', 'is' => new Zend_Db_Expr('not null')),
                        array('attribute' => 'news_to_date',   'is' => new Zend_Db_Expr('not null'))
                    ));
            }
        } elseif ($this->getMode() == 'promo') {

            $productCollection->addPriceData();
            $productCollection->getSelect()->where(sprintf('%s.final_price < %s.price',
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS,
                Mage_Catalog_Model_Resource_Product_Collection::INDEX_TABLE_ALIAS
            ));
        } elseif ($this->getMode() == 'bestsellers') {
            // Pretty awful
            $date     = new Zend_Date();
            $toDate   = $date->setDay(1)->getDate()->get('Y-MM-dd');
            $fromDate = $date->subMonth(3)->getDate()->get('Y-MM-dd');

            $storeId = (int) Mage::app()->getStore()->getId();

            // Reset product collection, sorry Magento
            $productCollection->getSelect()
                ->reset()
                ->from(array('aggregation' => $productCollection->getResource()->getTable('sales/bestsellers_aggregated_monthly')), null)
                ->joinLeft(
                    array('pr' => $productCollection->getResource()->getTable('catalog/product_relation')),
                    'aggregation.product_id = pr.child_id',
                    array('SUM(aggregation.qty_ordered) AS sold_quantity')
                )
                ->joinLeft(
                    array(Mage_Catalog_Model_Resource_Product_Collection::MAIN_TABLE_ALIAS => $productCollection->isEnabledFlat() ? $productCollection->getEntity()->getFlatTableName() : $productCollection->getEntity()->getEntityTable()),
                    'e.entity_id = pr.parent_id',
                    array('entity_id', 'type_id', 'attribute_set_id')
                )
                ->where("aggregation.store_id={$storeId} AND aggregation.period BETWEEN '{$fromDate}' AND '{$toDate}'")
                ->group('e.entity_id')
                ->order(array('sold_quantity DESC', 'e.created_at'));

            // Readd to product collection
            $productCollection
                ->addAttributeToSelect(Mage::getSingleton('catalog/config')->getProductAttributes())
                ->addStoreFilter()
                ->addPriceData()
                ->addTaxPercents()
                ->addUrlRewrite();

            Mage::getSingleton('catalog/product_status')->addVisibleFilterToCollection($productCollection);
            Mage::getSingleton('catalog/product_visibility')->addVisibleInCatalogFilterToCollection($productCollection);
        }
    }

    public function getCacheKeyInfo() {
        $uniqueTag = !is_null($this->getCategoryId()) ? $this->getCategoryId() : $this->getMode();

        $uniqueTag .= '_'.$this->getNameInLayout();
        if($cmsKey = Mage::getSingleton('cms/page')->getIdentifier())
            $uniqueTag .= '_'.$cmsKey;

        return array(
            'FEATUREDPRODUCTS_LIST_'.strtoupper($uniqueTag),
            Mage::app()->getStore()->getId(),
            (int)Mage::app()->getStore()->isCurrentlySecure(),
            Mage::getDesign()->getPackageName(),
            Mage::getDesign()->getTheme('template')
        );
    }


    /**
     * Automatically set link to category when mode is category
     *
     * @param [type] $id [description]
     */
    public function setCategoryId($id)
    {
        if (!is_null($id)) {
            $category = Mage::getModel('catalog/category')->load($id);
            if ($category && $category->getId()) {
                $this->setViewMoreLink($category->getUrl());
            }
        }
        return parent::setCategoryId($id);
    }

}
