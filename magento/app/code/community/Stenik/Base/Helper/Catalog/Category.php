<?php
/**
 * Category Helper
 *
 * @package  Stenik_Base
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Base_Helper_Catalog_Category extends Mage_Core_Helper_Abstract
{
    /**
     * Retrieve category's url using url params
     *
     * @param  Mage_Catalog_Model_Category $category
     * @param  string                      $urlParams
     * @return string
     */
    public function getCategoryUrl(Mage_Catalog_Model_Category $category, $urlParams = null)
    {
        if (!is_array($urlParams)) {
            return (string) $category->getUrl();
        }

        if (!isset($this->_categoryRequestPath[$category->getId()])) {
            Varien_Profiler::start(__METHOD__ . ' - load url rewrite');
            $this->_categoryRequestPath[$category->getId()] = Mage::getModel('core/url_rewrite')->loadByIdPath('category/' . $category->getId())->getRequestPath();
            Varien_Profiler::stop(__METHOD__ . ' - load url rewrite');
        }

        $urlParams['_direct'] = $this->_categoryRequestPath[$category->getId()];

        return Mage::getUrl('', $urlParams);
    }

    /**
     * Retrieve current category url with params
     *
     * @param  array $urlParams
     * @return string
     */
    public function getCurrentCategoryUrl($urlParams = null)
    {
        $currentCategory = Mage::registry('current_category');

        if (!$currentCategory) {
            $currentCategory = Mage::getSingleton('catalog/layer')->getCurrentCategory();
        }

        if ($currentCategory && $currentCategory->getId() && $currentCategory->getLevel() >= 2) {
            return $this->getCategoryUrl($currentCategory, $urlParams);
        }

        return '';
    }
}