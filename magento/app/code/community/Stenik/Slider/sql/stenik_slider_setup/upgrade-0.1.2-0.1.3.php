<?php
/**
 * Add new columns to the stenik_slider/slide table
 *
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

$installer = $this;
/* @var $installer Mage_Core_Model_Resource_Setup */

$installer->getConnection()->addColumn($installer->getTable('stenik_slider/slide'), 'frontend_title', array(
    'type'    => Varien_Db_Ddl_Table::TYPE_TEXT,
    'length'  => 255,
    'comment' => 'Frontend Title',
));
$installer->getConnection()->addColumn($installer->getTable('stenik_slider/slide'), 'frontend_subtitle', array(
    'type'    => Varien_Db_Ddl_Table::TYPE_TEXT,
    'length'  => 255,
    'comment' => 'Frontend Subtitle',
));