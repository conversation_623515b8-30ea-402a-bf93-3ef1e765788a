<?php
/**
 * @package  Stenik_Slider
 * <AUTHOR> <<EMAIL>>
 */

class Stenik_Slider_Model_Slide extends Mage_Core_Model_Abstract
{
	const STATUS_ENABLED  = 1;
	const STATUS_DISABLED = 0;

	const CACHE_TAG       = 'stenik_slider_slide';
	protected $_cacheTag  = 'stenik_slider_slide';

	/**
	 * init
	 */
	protected function _construct()
	{
		$this->_init('stenik_slider/slide');
	}

	/**
	 * Get Image Url
	 * @return string|null
	 */
	public function getImageUrl()
	{
		if (!$this->getImage())
			return null;

		return Mage::getBaseUrl('media') . $this->getImage();
	}
}