<?php
/**
 * Install product attribute for zero APR
 *
 * @package Stenik_LeasingTbi
 * <AUTHOR> Magento Team <<EMAIL>>
 */

$installer = new Mage_Catalog_Model_Resource_Setup('core_write');
$installer->startSetup();

$installer->addAttribute(Mage_Catalog_Model_Product::ENTITY, Stenik_LeasingTbi_Helper_Product::ATTRIBUTE_CODE_ZERO_APR, array(
    'type'                       => 'int',
    'label'                      => 'TBI Leasing - Zero APR',
    'input'                      => 'select',
    'source'                     => 'eav/entity_attribute_source_boolean',
    'required'                   => false,
    'user_defined'               => false,
    'searchable'                 => false,
    'filterable'                 => false,
    'used_in_product_listing'    => true,
    'comparable'                 => false,
    'visible_in_advanced_search' => false,
));

$installer->endSetup();