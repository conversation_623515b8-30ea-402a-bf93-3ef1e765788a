<?php
/**
 * Partner list view
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Partner_Block_Partner_ListPage extends Stenik_Partner_Block_Partner_List
{
    /**
     * Prepare Layout
     *
     * @return self
     */
    protected function _prepareLayout()
    {
        if ($headBlock = $this->getLayout()->getBlock('head')) {
            $headBlock->setTitle(Mage::helper('stenik_partner')->getPartnerListPageTitle());
        }

        if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb('home', array(
                'label' => Mage::helper('page')->__('Home'),
                'title' => Mage::helper('page')->__('Go to Home Page'),
                'link'  => Mage::getBaseUrl(),
            ));

            $breadcrumbsBlock->addCrumb('partner_list', array(
                'label' => Mage::helper('stenik_partner')->getPartnerListPageTitle(),
                'title' => Mage::helper('stenik_partner')->getPartnerListPageTitle(),
            ));
        }
        return $this;
    }
}