<?php
/**
 * Partnership Form Page
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

/**
 * @method this setMetaTitle(string $title)
 * @method null|string getMetaTitle()
 * @method boolean hasMetaTitle()
 */
class Stenik_Partner_Block_Partnership_FormPage extends Stenik_Partner_Block_Partnership_Form
{
    public function getPageTitle()
    {
        if (!$this->hasData('page_title')) {
            $this->setData('page_title', Mage::helper('stenik_partner/partnershipRequest')->getPageTitle());
        }

        return $this->getData('page_title');
    }

    /**
     * Prepare Layout
     *
     * @return self
     */
    protected function _prepareLayout()
    {

        $partnershipPageTitle = $this->getPageTitle();

        if ($headBlock = $this->getLayout()->getBlock('head')) {
            if ($this->hasMetaTitle()) {
                $headBlock->setTitle($this->getMetaTitle());
            } elseif ($partnershipPageTitle) {
                $headBlock->setTitle($partnershipPageTitle);
            }
        }

        if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb('home', array(
                'label' => Mage::helper('page')->__('Home'),
                'title' => Mage::helper('page')->__('Go to Home Page'),
                'link'  => Mage::getBaseUrl(),
            ));


            $breadcrumbsBlock->addCrumb('partnership_formpage', array(
                'label' => $partnershipPageTitle,
                'title' => $partnershipPageTitle,
            ));
        }

        return $this;
    }
}