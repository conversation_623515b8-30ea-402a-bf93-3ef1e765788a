<?php
/**
 * Group Grid Container
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Partner_Block_Adminhtml_Group extends Mage_Adminhtml_Block_Widget_Grid_Container
{
    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_blockGroup = 'stenik_partner';
        $this->_controller = 'adminhtml_group';
        $this->_headerText = Mage::helper('stenik_partner')->__('Group Manager');
        $this->_addButtonLabel = Mage::helper('stenik_partner')->__('Add Group');
        parent::_construct();
    }
}