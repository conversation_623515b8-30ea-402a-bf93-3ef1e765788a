<?php
/**
 * Group Edit Form Container
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Partner_Block_Adminhtml_Group_Edit extends Mage_Adminhtml_Block_Widget_Form_Container
{
    protected function _construct()
    {
        parent::_construct();

        $this->_objectId = 'id';
        $this->_blockGroup = 'stenik_partner';
        $this->_controller = 'adminhtml_group';

        $this->_updateButton('save', 'label', Mage::helper('stenik_partner')->__('Save Group'));
        $this->_updateButton('delete', 'label', Mage::helper('stenik_partner')->__('Delete Group'));

        $this->_addButton('saveandcontinue', array(
            'label'     => Mage::helper('adminhtml')->__('Save And Continue Edit'),
            'onclick'   => 'saveAndContinueEdit()',
            'class'     => 'save',
        ), -100);

        $this->_formScripts[] = "
            function saveAndContinueEdit(){
                editForm.submit($('edit_form').action+'back/edit/');
            }

            function checkMaxLength(Object, MaxLen) {
                if (Object.value.length > MaxLen-1) {
                    Object.value = Object.value.substr(0, MaxLen);
                }
                return 1;
            }
        ";
    }

    public function getHeaderText()
    {
        if( Mage::registry('stenik_partner_group') && Mage::registry('stenik_partner_group')->getId() ) {
            return Mage::helper('stenik_partner')->__("Edit Group '%s' (ID: %s)", $this->htmlEscape(Mage::registry('stenik_partner_group')->getName()), Mage::registry('stenik_partner_group')->getId());
        } else {
            return Mage::helper('stenik_partner')->__('Add Group');
        }
    }
}
