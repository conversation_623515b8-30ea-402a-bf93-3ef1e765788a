<?php
/**
 * Abstract Image Attribute Frontend Model
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

abstract class Stenik_Partner_Model_Abstract_Attribute_Frontend_Image extends Mage_Eav_Model_Entity_Attribute_Frontend_Abstract
{
    /**
     * Item String
     *
     * @var string
     */
    protected $_item = '';

    /**
     * Retrieve Image Url
     *
     * @param  Varien_Object $object
     * @param  string $size
     * @return string|false
     */
    public function getUrl($object, $size=null)
    {
        $url = false;
        $image = $object->getData($this->getAttribute()->getAttributeCode());

        if( !is_null($size) && file_exists(Mage::getBaseDir('media').DS.'stenik_partner'.DS.$this->_item.DS.$size.DS.$image) ) {
            # resized image is cached
            $url = Mage::app()->getStore($object->getStore())->getBaseUrl('media').'stenik_partner/' . $this->_item . '/' . $size . '/' . $image;
        } elseif( !is_null($size) ) {
            # resized image is not cached
            $url = Mage::app()->getStore($object->getStore())->getBaseUrl().'stenik_partner/' . $this->_item . '/image/size/' . $size . '/' . $image;
        } elseif ($image) {
            # using original image
            $url = Mage::app()->getStore($object->getStore())->getBaseUrl('media').'stenik_partner/' . $this->_item . '/'.$image;
        }
        return $url;
    }

}
