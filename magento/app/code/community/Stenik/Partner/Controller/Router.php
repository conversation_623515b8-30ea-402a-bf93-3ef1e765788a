<?php
/**
 * Partners and Groups Router
 *
 * @package Stenik_Partner
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Partner_Controller_Router extends Mage_Core_Controller_Varien_Router_Abstract
{
    /**
     * Initialize Controller Router
     *
     * @param Varien_Event_Observer $observer
     */
    public function initControllerRouters($observer)
    {
        /* @var $front Mage_Core_Controller_Varien_Front */
        $front = $observer->getEvent()->getFront();

        $front->addRouter('stenik_partner', $this);
    }

    /**
     * Validate and Match Pages and modify request
     *
     * @param Zend_Controller_Request_Http $request
     * @return bool
     */
    public function match(Zend_Controller_Request_Http $request)
    {
        if (!Mage::isInstalled()) {
            return false;
        }

        Varien_Profiler::start(__METHOD__);
        $identifier = trim($request->getPathInfo(), '/');

        $condition = new Varien_Object(array(
            'identifier' => $identifier,
            'continue'   => true
        ));

        Mage::dispatchEvent('stenik_partner_controller_router_match_before', array(
            'router'    => $this,
            'condition' => $condition
        ));
        $identifier = $condition->getIdentifier();

        if ($condition->getRedirectUrl()) {
            Mage::app()->getFrontController()->getResponse()
                ->setRedirect($condition->getRedirectUrl())
                ->sendResponse();
            $request->setDispatched(true);
            Varien_Profiler::stop(__METHOD__);
            return true;
        }

        if (!$condition->getContinue()) {
            Varien_Profiler::stop(__METHOD__);
            return false;
        }


        $matched = false;

        $partnerListEnabled = Mage::helper('stenik_partner')->getIsPartnerListEnabled();
        $partnerListUrlKey  = Mage::helper('stenik_partner')->getPartnerListUrlKey();
        $groupListEnabled   = Mage::helper('stenik_partner')->getIsGroupListEnabled();
        $groupListUrlKey    = Mage::helper('stenik_partner')->getGroupListUrlKey();

        $partnershipRequestUrlKey = sprintf('%s%s%s',
            Mage::helper('stenik_partner/partnershipRequest')->getUrlPrefix(),
            Mage::helper('stenik_partner/partnershipRequest')->getUrlKey(),
            Mage::helper('stenik_partner/partnershipRequest')->getUrlSuffix()
        );

        if ($partnerListEnabled && $partnerListUrlKey && preg_match('/^' . preg_quote($partnerListUrlKey, '/') . '$/', $identifier)) {
            # partner list matched

            $request->setModuleName('stenik_partner')
                ->setControllerName('partner')
                ->setActionName('list');
            $matched = true;
        } elseif ($groupListEnabled && $groupListUrlKey && preg_match('/^' . preg_quote($groupListUrlKey, '/') . '$/', $identifier)) {
            # group list matched

            $request->setModuleName('stenik_partner')
                ->setControllerName('group')
                ->setActionName('list');
            $matched = true;
        } elseif (Mage::helper('stenik_partner/partnershipRequest')->isEnabled() && $partnershipRequestUrlKey && preg_match('/^' . preg_quote($partnershipRequestUrlKey, '/') . '$/', $identifier)) {
            # partnership request page matched

            $request->setModuleName('stenik_partner')
                ->setControllerName('partnership')
                ->setActionName('request');
            $matched = true;
        }

        if (!$matched) {
            // match partner
            $matched = $this->_matchItemView(new Varien_Object(array(
                'url_prefix' => Mage::helper('stenik_partner/partner')->getUrlPrefix(),
                'url_suffix' => Mage::helper('stenik_partner/partner')->getUrlSuffix(),
                'model'      => Mage::getModel('stenik_partner/partner'),
                'controller' => 'partner',
                'request'    => $request,
                'identifier' => $identifier,
            )));
        }

        if (!$matched) {
            // match group
            $matched = $this->_matchItemView(new Varien_Object(array(
                'url_prefix' => Mage::helper('stenik_partner/group')->getUrlPrefix(),
                'url_suffix' => Mage::helper('stenik_partner/group')->getUrlSuffix(),
                'model'      => Mage::getModel('stenik_partner/group'),
                'controller' => 'group',
                'request'    => $request,
                'identifier' => $identifier,
            )));
        }

        if (!$matched) {
            return false;
        }

        $request->setAlias(
            Mage_Core_Model_Url_Rewrite::REWRITE_REQUEST_PATH_ALIAS,
            $identifier
        );

        Varien_Profiler::stop(__METHOD__);
        return true;
    }


    /**
     * Match item view
     *
     * @param  Varien_Object $matchData
     * @return boolean
     */
    protected function _matchItemView(Varien_Object $matchData)
    {
        $urlPrefix  = $matchData->getUrlPrefix();
        $urlSuffix  = $matchData->getUrlSuffix();
        $model      = $matchData->getModel();
        $controller = $matchData->getController();
        $request    = $matchData->getRequest();
        $identifier = $matchData->getIdentifier();

        if (!$model || !$controller || !$request || !$identifier) {
            return false;
        }

        $matches = array();
        if (preg_match('/^' . preg_quote($urlPrefix, '/') . '(.*)' . preg_quote($urlSuffix, '/') . '$/', $identifier, $matches)) {
            if (isset($matches[1]) /* of course it's set */) {
                $item = $model->getCollection()
                    ->addAttributeToFilter('url_key', $matches[1])
                    ->addAttributeToFilter('is_active', Stenik_Partner_Model_Abstract_Attribute_Source_Status::STATUS_ENABLED)
                    ->setPageSize(1)
                    ->getFirstItem();

                if (!$item->getId()) {
                    // look at ___from_store
                    if ($fromStore = $request->getParam('___from_store')) {
                        $fromStoreId = Mage::app()->getStore($fromStore)->getId();
                        if ($fromStoreId) {
                            $item = $model->getCollection()
                                ->setStoreId($fromStoreId)
                                ->addAttributeToFilter('url_key', $matches[1])
                                ->addAttributeToFilter('is_active', Stenik_Partner_Model_Abstract_Attribute_Source_Status::STATUS_ENABLED)
                                ->setPageSize(1)
                                ->getFirstItem();

                            if ($item->getId()) {
                                Mage::app()->getFrontController()->getResponse()
                                    ->setRedirect($item->load($item->getId())->getUrl())
                                    ->sendResponse();
                                exit;
                            }
                        }
                    }
                }

                if ($item->getId()) {
                    $request->setModuleName('stenik_partner')
                        ->setControllerName($controller)
                        ->setActionName('view')
                        ->setParam('id', $item->getId());
                    return true;
                }
            }
        }

        return false;
    }
}