<?php

class Stenik_CategoryTemplate_Model_Observer {

    /**
     * Adds layout handle CATEGORY_CUSTOM_TEMPLATE_<custom_template> after
     * CATEGORY_<id> handle
     *
     * Event: controller_action_layout_load_before
     *
     * @param Varien_Event_Observer $observer
     */
    public function addAttributeSetHandle(Varien_Event_Observer $observer) {
        $category = Mage::registry('current_category');

        if (!($category instanceof Mage_Catalog_Model_Category))
            return;

        if(!($customTemplate = $category->getCustomTemplate()))
            return;

        $customTemplate = str_replace('-', '_', $customTemplate);

        $layout  = $observer->getEvent()->getLayout();
        $handles = $layout->getUpdate()->getHandles();

        // Reset all handles
        $layout->getUpdate()->resetHandles();

        // Add them anew
        foreach ($handles as $handle) {
            // Ensure custom handle is BEFORE specific category handle
            if ($handle == 'CATEGORY_' . $category->getId()) {
                $layout->getUpdate()->addHandle('CATEGORY_CUSTOM_TEMPLATE_' . $customTemplate);
            }
            $layout->getUpdate()->addHandle($handle);
        }
    }
}