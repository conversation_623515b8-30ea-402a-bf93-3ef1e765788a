<?php
/**
 * Shop Attribute Resource Collection
 * 
 * @package Stenik_Shop
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_Shop_Model_Resource_Shop_Attribute_Collection  extends Mage_Eav_Model_Resource_Entity_Attribute_Collection
{
    /**
     * Add attribute set info flag
     *
     * @var boolean
     */
    protected $_addSetInfoFlag   = true;

    /**
     * Main select object initialization.
     * Joins stenik_shop/eav_attribute table
     *
     * @return self
     */
    protected function _initSelect()
    {
        $this->getSelect()->from(array('main_table' => $this->getResource()->getMainTable()))
            ->where('main_table.entity_type_id=?', Mage::getModel('eav/entity')->setType(Stenik_Shop_Model_Shop::ENTITY)->getTypeId())
            ->join(
                array('additional_table' => $this->getTable('stenik_shop/eav_attribute')),
                'additional_table.attribute_id = main_table.attribute_id'
            );
        return $this;
    }

    /**
     * Specify attribute entity type filter
     *
     * @param int $typeId
     * @return self
     */
    public function setEntityTypeFilter($typeId)
    {
        return $this;
    }

    /**
     * Return array of fields to load attribute values
     *
     * @return array
     */
    protected function _getLoadDataFields()
    {
        $fields = array_merge(
            parent::_getLoadDataFields(),
            array(
                'additional_table.is_global',
                'additional_table.is_wysiwyg_enabled'
            )
        );

        return $fields;
    }
}
