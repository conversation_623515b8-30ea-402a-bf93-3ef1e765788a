<?php
/**
 * Shop Edit Tab Attributes
 *
 * @package Stenik_Shop
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_Shop_Block_Adminhtml_Shop_Edit_Tab_Attributes extends Mage_Adminhtml_Block_Widget_Form
{
    /**
     * Load Wysiwyg on demand and prepare layout
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if (Mage::helper('stenik_shop')->isModuleEnabled('Mage_Cms')
            && Mage::getSingleton('cms/wysiwyg_config')->isEnabled()
        ) {
            $this->getLayout()->getBlock('head')->setCanLoadTinyMce(true);
        }
    }

    /**
     * Prepare attributes form
     *
     * @return null
     */
    protected function _prepareForm()
    {
        Varien_Data_Form::setElementRenderer(
            $this->getLayout()->createBlock('adminhtml/widget_form_renderer_element')
        );
        Varien_Data_Form::setFieldsetRenderer(
            $this->getLayout()->createBlock('adminhtml/widget_form_renderer_fieldset')
        );
        Varien_Data_Form::setFieldsetElementRenderer(
            $this->getLayout()->createBlock('stenik_shop/adminhtml_form_renderer_fieldset_element')
        );
        $group = $this->getGroup();
        if ($group) {
            $form = new Varien_Data_Form();

            // Initialize shop object as form property to use it during elements generation
            $form->setDataObject(Mage::registry('stenik_shop_shop'));

            $fieldset = $form->addFieldset('group_fields' . $group->getId(), array(
                'legend' => Mage::helper('stenik_shop')->__($group->getAttributeGroupName()),
                'class' => 'fieldset-wide'
            ));

            $attributes = $this->getGroupAttributes();

            $this->_setFieldset($attributes, $fieldset, array(/*'media_gallery'*/));

            if ($form->getElement('meta_description')) {
                $form->getElement('meta_description')->setOnkeyup('checkMaxLength(this, 255);');
            }

            $values = Mage::registry('stenik_shop_shop')->getData();

            // Set default attribute values for new stenik_shop
            if (!Mage::registry('stenik_shop_shop')->getId()) {
                foreach ($attributes as $attribute) {
                    if (!isset($values[$attribute->getAttributeCode()])) {
                        $values[$attribute->getAttributeCode()] = $attribute->getDefaultValue();
                    }
                }
            }

            if (Mage::registry('stenik_shop_shop')->hasLockedAttributes()) {
                foreach (Mage::registry('stenik_shop_shop')->getLockedAttributes() as $attribute) {
                    $element = $form->getElement($attribute);
                    if ($element) {
                        $element->setReadonly(true, true);
                    }
                }
            }
            $form->addValues($values);
            $form->setFieldNameSuffix('stenik_shop');

            Mage::dispatchEvent('stenik_shop_adminhtml_edit_prepare_form', array('form' => $form));

            $this->setForm($form);
        }
    }

    /**
     * Retrieve additional element types
     *
     * @return array
     */
    protected function _getAdditionalElementTypes()
    {
        $result = array(
            'textarea'  => Mage::getConfig()->getBlockClassName('stenik_shop/adminhtml_shop_edit_renderer_wysiwyg'),
            'gallery'  => Mage::getConfig()->getBlockClassName('stenik_shop/adminhtml_shop_edit_renderer_gallery'),
        );

        $response = new Varien_Object();
        $response->setTypes(array());
        Mage::dispatchEvent('stenik_shop_adminhtml_edit_element_types', array('response' => $response));

        foreach ($response->getTypes() as $typeName => $typeClass) {
            $result[$typeName] = $typeClass;
        }

        return $result;
    }
}
