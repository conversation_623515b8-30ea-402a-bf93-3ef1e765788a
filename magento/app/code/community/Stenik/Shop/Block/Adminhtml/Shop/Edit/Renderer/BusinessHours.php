<?php
/**
 * @package  Stenik_Shop
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_Shop_Block_Adminhtml_Shop_Edit_Renderer_BusinessHours
    extends Varien_Data_Form_Element_Abstract
{
    /**
     * Init styles
     * 
     * @return void
     */
    protected function _construct()
    {

    }

    /**
     * Retrieve element html
     * 
     * @return string
     */
    public function getElementHtml()
    {
        $html = '<table cellspacing="8">';
        $this->setData('class', $this->getData('class') . ' select-date');
        $elementValue = $this->getValue();

        $weekDays = Mage::helper('stenik_base/locale')->getWeekDays();

        // Generate for each day of the week two selects from and to
        $html .= '<tr><td></td><td>'
            . Mage::helper('stenik_shop')->__('From')
            . '</td><td></td><td>'
            . Mage::helper('stenik_shop')->__('To')
            . '</td></tr>';
        foreach ($weekDays as $dayCode => $day) {
            $html .= '<tr>';
            $html .= '<td align="right">' . $day .'</td>';
            foreach (array('from', 'to') as $fromto) {

                if ($fromto == 'to') $html .= '<td>-</td>';

                $optionsHtml = '<option value="">-----------------</option>';

                $hours = range(0, 24);
                $minutesStep = (int) Mage::helper('stenik_shop')->getBusinessHoursIntervalInMinutes();
                if ($minutesStep > 0 && $minutesStep < 60)
                    $minutes = range(0, 59, (int)Mage::helper('stenik_shop')->getBusinessHoursIntervalInMinutes());
                else $minutes = array(0);
                foreach ($hours as $hour)
                    foreach ($minutes as $minute) {
                        if ($hour == 24 && $minute != 0) break;
                        $optionValue = sprintf('%02d:%02d', $hour, $minute);
                        if (is_array($elementValue)
                            && isset($elementValue[$dayCode][$fromto])
                            && $elementValue[$dayCode][$fromto] == $optionValue
                        )
                            $optionsHtml .= sprintf('<option selected="selected">%s</option>', $optionValue);
                        else $optionsHtml .= sprintf('<option>%s</option>', $optionValue);
                    }

                $html .= sprintf('<td><select id="%s" name="%s[%s][%s]" %s>%s</select></td>',
                    $this->getHtmlId() . '_' . $dayCode . '_' . $fromto,
                    $this->getName(),
                    $dayCode,
                    $fromto,
                    $this->serialize($this->getHtmlAttributes()),
                    $optionsHtml
                );
            }
            
            $html .= '</tr>';
        }

        $html .= '</table>';

        $html.= $this->getAfterElementHtml();
        return $html;
    }
}