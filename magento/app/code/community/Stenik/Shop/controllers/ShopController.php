<?php
/**
 * Shop Controller
 *
 * @package Stenik_Shop
 * <AUTHOR> Stenik Group Ltd. <<EMAIL>>
 */

class Stenik_Shop_ShopController extends Mage_Core_Controller_Front_Action
{
    /**
     * List Action
     * 
     * @return void
     */
    public function listAction()
    {
        $this->loadLayout();
        $this->renderLayout();
    }

    /**
     * View Action
     * 
     * @return void
     */
    public function viewAction()
    {
        $shopId = $this->getRequest()->getParam('id');

        if (!$shopId)
            return $this->_forward('noRoute');

        $shop = Mage::getModel('stenik_shop/shop')->load($shopId);

        if (!$shop->getId())
            return $this->_forward('noRoute');

        if (!$shop->getIsActive())
            return $this->_forward('noRoute');

        Mage::register('stenik_shop_shop', $shop);

        $this->loadLayout();
        $this->renderLayout();
    }
}