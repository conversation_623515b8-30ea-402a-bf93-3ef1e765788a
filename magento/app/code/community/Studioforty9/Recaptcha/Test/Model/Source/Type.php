<?php
/**
 * Studioforty9_Recaptcha
 *
 * @category  Studioforty9
 * @package   Studioforty9_Recaptcha
 * <AUTHOR> <<EMAIL>>
 * @copyright 2015 StudioForty9 (http://www.studioforty9.com)
 * @license   https://github.com/studioforty9/recaptcha/blob/master/LICENCE BSD
 * @version   1.5.7
 * @link      https://github.com/studioforty9/recaptcha
 */

/**
 * Studioforty9_Recaptcha_Test_Model_Source_Type
 *
 * @category   Studioforty9
 * @package    Studioforty9_Recaptcha
 * @subpackage Test
 * <AUTHOR> <<EMAIL>>
 */
class Studioforty9_Recaptcha_Test_Model_Source_Type extends EcomDev_PHPUnit_Test_Case
{
    /** @var Studioforty9_Recaptcha_Model_Source_Type $observer */
    protected $model;

    public function setUp()
    {
        $this->model = new Studioforty9_Recaptcha_Model_Source_Type();
    }

    /**
     * @test
     * @group source
     * @group Recaptcha
     */
    public function it_returns_expected_array_items()
    {
        $options = $this->model->toOptionArray();

        $this->assertArrayHasKey('image', $options);
        $this->assertArrayHasKey('audio', $options);

        $this->assertEquals('Image', $options['image']);
        $this->assertEquals('Audio', $options['audio']);
    }
}
