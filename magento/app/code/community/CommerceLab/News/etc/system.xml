<?xml version="1.0"?>
<config>
    <tabs>
        <clall translate="label" module="clnews">
            <label>CommerceLab Extensions</label>
            <sort_order>300</sort_order>
        </clall>
    </tabs>
    <sections>
        <clnews translate="label" module="clnews">
            <label>News</label>
            <tab>clall</tab>
            <frontend_type>text</frontend_type>
            <sort_order>100</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <news translate="label">
                    <label>News Settings</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <title translate="label">
                            <label>News Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </title>
                        <route translate="label">
                            <label>Route to News</label>
                            <comment>e.g. "news" will make the news accessible from domain.com/news. Should not contain slashes. Leave blank for default - news</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>12</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </route>
                        <showbreadcrumbs translate="label">
                            <label>Show Breadcrumbs</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showbreadcrumbs>
                        <itemsperpage translate="label">
                            <label>Articles per Page</label>
                            <comment>The number of articles displayed per page</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </itemsperpage>
                        <itemslimit translate="label">
                            <label>Articles Limit</label>
                            <comment>Limit for the total number of articles</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </itemslimit>
                        <showrightblock translate="label">
                            <label>Show News Category Block in Right Column</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>31</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showrightblock>
                        <showleftblock translate="label">
                            <label>Show News Category Block in Left Column</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>32</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showleftblock>
                        <showlatestnews translate="label">
                            <label>Show Latest News Block</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>33</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showlatestnews>
                        <latestitemscount translate="label">
                            <label>Q-ty in the Latest Block</label>
                            <comment>Set the number</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </latestitemscount>
                        <showdateofnews translate="label">
                            <label>Show Date of the News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>36</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showdateofnews>
                        <showtimeofnews translate="label">
                            <label>Show Time of the News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>37</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showtimeofnews>
                        <showauthorofnews translate="label">
                            <label>Show Author of the News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>38</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showauthorofnews>
                        <showcategoryofnews translate="label">
                            <label>Show Category of the News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>39</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </showcategoryofnews>
                        <enablelinkrout translate="label">
                            <label>Enable Link "View More"</label>
                            <comment>default: view more</comment>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enablelinkrout>
                        <linkrout translate="label">
                            <label>Name of the Link</label>
                            <comment>example: view more</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>41</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </linkrout>
                        <itemurlsuffix translate="label">
                            <label>Newsitem URL Suffix</label>
                            <comment>example: .html</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>41</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </itemurlsuffix>
                        <tags translate="label">
                            <label>Tags for the News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>42</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </tags>
                        <google translate="label">
                            <label>Enable Google Button for News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>43</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </google>
                        <twitter translate="label">
                            <label>Enable Twitter Button for News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>44</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </twitter>
                        <facebook translate="label">
                            <label>Enable Facebook Button for News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>45</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </facebook>
                        <linked_in translate="label">
                            <label>Enable LinkedIn Button for News</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>46</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </linked_in>
                        <metatitle translate="label">
                            <label>Default Meta Title</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>47</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </metatitle>
                        <metakeywords translate="label">
                            <label>Default Meta Keywords</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </metakeywords>
                        <metadescription translate="label">
                            <label>Default Meta Description</label>
                            <frontend_type>textarea</frontend_type>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </metadescription>
                        <shortdescr_image_max_width translate="label">
                            <label>Short Description Image Max Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </shortdescr_image_max_width>
                        <shortdescr_image_max_height translate="label">
                            <label>Short Description Image Max Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </shortdescr_image_max_height>
                        <fulldescr_image_max_width translate="label">
                            <label>Full Description Image Max Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </fulldescr_image_max_width>
                        <fulldescr_image_max_height translate="label">
                            <label>Full Description Image Max Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </fulldescr_image_max_height>
                        <resize_to_max translate="label">
                            <label>Resize always to Max Values</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>101</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </resize_to_max>
                    </fields>
                </news>
                <comments translate="label">
                    <label>Comments</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>40</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <recipient_email translate="label">
                            <label>Send comments to email</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </recipient_email>
                        <sender_email_identity translate="label">
                            <label>Email Sender</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_email_identity</source_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </sender_email_identity>
                        <email_template translate="label">
                            <label>Email Template</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_email_template</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </email_template>
                        <need_confirmation translate="label">
                            <label>Should comments be pre-moderated</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </need_confirmation>
                        <need_login translate="label">
                            <label>Should user be logged in to leave a comment</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>71</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </need_login>
                        <commentsperpage translate="label">
                            <label>Comments per Page</label>
                            <comment>The number of comments displayed per page</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>75</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </commentsperpage>
                    </fields>
                </comments>
                <rss translate="label">
                    <label>RSS Feed</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>60</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enable translate="label">
                            <label>Enable</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>1</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enable>
                        <posts translate="label">
                            <label>Articles to show</label>
                            <comment>Number of articles to show in the RSS feed.</comment>
                            <frontend_type>text</frontend_type>
                            <sort_order>2</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </posts>
                    </fields>
                </rss>
            </groups>
        </clnews>
    </sections>
</config>
