<?xml version="1.0"?>
<config>
    <modules>
        <IWD_All>
            <version>2.2.2</version>
        </IWD_All>
    </modules>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <iwdall after="Mage_Adminhtml">IWD_All_Adminhtml</iwdall>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <adminhtml>
        <translate>
            <modules>
                <IWD_All>
                    <files>
                        <default>IWD_All.csv</default>
                    </files>
                </IWD_All>
            </modules>
        </translate>

        <layout>
            <updates>
                <iwdall>
                    <file>iwdall.xml</file>
                </iwdall>
            </updates>
        </layout>      
    </adminhtml>

    <global>
        <models>
            <paygate>
                <rewrite>
                    <authorizenet>IWD_All_Model_Paygate_Authorizenet</authorizenet>
                </rewrite>
            </paygate>
            <paypal>
                <rewrite>
                    <config>IWD_All_Model_Paypal_Config</config>
                </rewrite>
            </paypal>
            <braintree_payments>
                <rewrite>
                    <paymentmethod>IWD_All_Model_Braintree_Paymentmethod</paymentmethod>
                </rewrite>
            </braintree_payments>
            <iwdall>
                <class>IWD_All_Model</class>
                <resourceModel>iwdall_mysql4</resourceModel>
            </iwdall>

            <iwdall_mysql4>
                <class>IWD_All_Model_Mysql4</class>
                <entities>
                   
                </entities>
            </iwdall_mysql4>
        </models>

        <resources>
            <iwdall_setup>
                <setup>
                    <module>IWD_All</module>
                </setup>
            </iwdall_setup>

            <iwdall_write>
                <connection>
                    <use>core_write</use>
                </connection>
            </iwdall_write>

            <iwdall_read>
                <connection>
                    <use>core_read</use>
                </connection>
            </iwdall_read>
        </resources>

        <helpers>
            <iwdall>
                <class>IWD_All_Helper</class>
            </iwdall>
        </helpers>

        <blocks>
            <iwdall>
                <class>IWD_All_Block</class>
            </iwdall>
        </blocks>
    </global>

    <frontend>
        <layout>
            <updates>
                <iwdall>
                    <file>iwd_all.xml</file>
                </iwdall>
            </updates>
        </layout>
        <routers>
            <iwdall>
                <use>standard</use>
                <args>
                    <module>IWD_All</module>
                    <frontName>iwdextensions</frontName>
                </args>
            </iwdall>
        </routers>
    </frontend>
</config>