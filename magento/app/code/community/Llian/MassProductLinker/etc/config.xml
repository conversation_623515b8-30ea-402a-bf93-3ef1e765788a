<?xml version="1.0" encoding="UTF-8"?>
<config>
	<modules>
		<Llian_MassProductLinker>
			<version>0.2.2</version>
		</Llian_MassProductLinker>
	</modules>
	<global>
		<helpers>
			<llianmassproductlinker>
				<class>Llian_MassProductLinker_Helper</class>
			</llianmassproductlinker>
		</helpers>
	</global>
	<adminhtml>
		<events>
			<core_block_abstract_to_html_before>
				<observers>
					<Llian_MassProductLinker_Model_Observer>
						<type>singleton</type>
						<class>Llian_MassProductLinker_Model_Observer</class>
						<method>addMassAction</method>
					</Llian_MassProductLinker_Model_Observer>
				</observers>
			</core_block_abstract_to_html_before>
		</events>
		<translate>
			<modules>
				<Llian_MassProductLinker>
					<files>
						<llianmassproductlinker>Llian_MassProductLinker.csv</llianmassproductlinker>
					</files>
				</Llian_MassProductLinker>
			</modules>
		</translate>
	</adminhtml>
	<admin>
		<routers>
			<adminhtml>
				<args>
					<modules>
						<massproductlinker after="Mage_Adminhtml">Llian_MassProductLinker</massproductlinker>
					</modules>
				</args>
			</adminhtml>
		</routers>
	</admin>
	<default>
		<catalog>
			<massproductlinker>
				<cross_sell_to_each_other>1</cross_sell_to_each_other>
				<cross_sell_to>1</cross_sell_to>
				<un_cross_sell_to_each_other>1</un_cross_sell_to_each_other>
				<un_cross_sell>1</un_cross_sell>
				<up_sell_to_each_other>0</up_sell_to_each_other>
				<up_sell_to>1</up_sell_to>
				<un_up_sell_to_each_other>0</un_up_sell_to_each_other>
				<un_up_sell>1</un_up_sell>
				<relate_to_each_other>0</relate_to_each_other>
				<relate_to>1</relate_to>
				<un_relate_to_each_other>0</un_relate_to_each_other>
				<un_relate>1</un_relate>
			</massproductlinker>
		</catalog>
	</default>
</config>