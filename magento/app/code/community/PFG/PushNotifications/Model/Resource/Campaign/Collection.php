<?php

class PFG_PushNotifications_Model_Resource_Campaign_Collection extends Mage_Core_Model_Resource_Db_Collection_Abstract
{

    protected function _construct()
    {
        $this->_init('pfg_pn/campaign');
    }

    public function addRunningFilter()
    {
        $this->addFieldToFilter('status', PFG_PushNotifications_Model_Campaign::STATUS_RUNNING);

        return $this;
    }

    public function addScheduledFilter()
    {
        $this->addFieldToFilter('status', PFG_PushNotifications_Model_Campaign::STATUS_SCHEDULED);

        return $this;
    }
}