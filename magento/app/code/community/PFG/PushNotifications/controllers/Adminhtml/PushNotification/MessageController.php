<?php

class PFG_PushNotifications_Adminhtml_PushNotification_MessageController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Init layout and breadcrumbs
     *
     * @return self
     */
    protected function _initAction()
    {
        $this->loadLayout()
            ->_setActiveMenu('pfg_pn/messages');

        return $this;
    }

    public function indexAction()
    {
        $this->_initAction();
        $this->renderLayout();
    }

    /**
     * Check permissions before allow edit list of blocks
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('admin/pfg_pn/messages');
    }
}