<?php
/**
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
class Extensa_Econt_Model_System_Config_Source_Attribute
{
    /**
     * Returns array to be used in select on back-end
     *
     * @return array
     */
    public function toOptionArray()
    {
        $options = array();

        $productAttrs = Mage::getResourceModel('catalog/product_attribute_collection');
        foreach ($productAttrs as $productAttr) {
            if (!$productAttr->getFrontendLabel()) {
                continue;
            }
            $options[] = array(
                'label' => $productAttr->getFrontendLabel(),
                'value' => $productAttr->getAttributeCode()
            );
        }

        return $options;
    }
}
