<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2021 Amasty (https://www.amasty.com)
 * @package Amasty_SeoSingleUrl
 */

class Amasty_SeoSingleUrl_Helper_Data extends Mage_Core_Helper_Abstract
{
	const PRODUCT_URL_PATH_DEFAULT  = 0;
	const PRODUCT_URL_PATH_SHORTEST = 1;
	const PRODUCT_URL_PATH_LONGEST  = 2;

	/**
	 * @return bool
	 */
	public static function urlRewriteHelperEnabled()
	{
		return version_compare(Mage::getVersion(), '1.8') >= 0;
	}

	/**
	 * Product url type (shortest/longest/default)
	 *
	 * @return mixed
	 */
	public function getProductUrlType()
	{
		return Mage::getStoreConfig('amseourl/general/product_url_type');
	}

	/**
	 * @return bool
	 */
	public function useDefaultProductUrlRules()
	{
		return (int) $this->getProductUrlType() == self::PRODUCT_URL_PATH_DEFAULT
			   || ! Mage::getStoreConfig(Mage_Catalog_Helper_Product::XML_PATH_PRODUCT_URL_USE_CATEGORY);
	}

    /**
     * @param string $string
     *
     * @return array|null
     */
    public function unserialize($string)
    {
        if (!@class_exists('Amasty_Base_Helper_String')) {
            $message = $this->getUnserializeError();
            Mage::logException(new Exception($message));
            if (Mage::app()->getStore()->isAdmin()) {
                Mage::helper('ambase/utils')->_exit($message);
            } else {
                Mage::throwException($this->__('Sorry, something went wrong. Please contact us or try again later.'));
            }
        }

        return \Amasty_Base_Helper_String::unserialize($string);
    }

    /**
     * @return string
     */
    public function getUnserializeError()
    {
        return 'If there is the following text it means that Amasty_Base is not updated to the latest 
                             version.<p>In order to fix the error, please, download and install the latest version of 
                             the Amasty_Base, which is included in all our extensions.
                        <p>If some assistance is needed, please submit a support ticket with us at: '
            . '<a href="https://amasty.com/contacts/" target="_blank">https://amasty.com/contacts/</a>';
    }
}