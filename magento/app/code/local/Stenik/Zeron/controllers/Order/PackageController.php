<?php
/**
 * Order Package Controller
 *
 * @package Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Zeron_Order_PackageController extends Mage_Core_Controller_Front_Action
{
    /**
     * View Action
     *
     * @return void
     */
    public function viewAction()
    {
        $package = $this->_initPackage();
        if (!$package->getId() ||
            $package->getSecureCode() != $this->getRequest()->getParam('sc') ||
            $package->getStatus() != Stenik_Zeron_Model_Package_Attribute_Source_Status::STATUS_PENDING_APPROVAL
        ) {
            return $this->norouteAction();
        }

        $this->loadLayout();
        $this->renderLayout();
    }

    /**
     * Approve Action
     *
     * @return void
     */
    public function approveAction()
    {
        $package = $this->_initPackage();
        if (!$package->getId() ||
            $package->getSecureCode() != $this->getRequest()->getParam('sc') ||
            $package->getStatus() != Stenik_Zeron_Model_Package_Attribute_Source_Status::STATUS_PENDING_APPROVAL
        ) {
            return $this->norouteAction();
        }

        $dataToSave = array('readiness_date', 'fragile', 'package_count', 'type', 'note');
        foreach ($dataToSave as $dataKey) {
            $package->setData($dataKey,
                $this->getRequest()->getPost($dataKey)
            );
        }

        $package->setStatus(Stenik_Zeron_Model_Package_Attribute_Source_Status::STATUS_APPROVED);
        $package->save();

        $order = $package->getOrder();
        $order->setState(Mage_Sales_Model_Order::STATE_PROCESSING, 'zeron_approved', sprintf('Прието запитване %s', $package->getIncrementId()));
        $order->save();

        $this->loadLayout();
        $this->renderLayout();
    }

    /**
     * Dispprove Action
     *
     * @return void
     */
    public function disapproveAction()
    {
        $package = $this->_initPackage();
        if (!$package->getId() ||
            $package->getSecureCode() != $this->getRequest()->getParam('sc') ||
            $package->getStatus() != Stenik_Zeron_Model_Package_Attribute_Source_Status::STATUS_PENDING_APPROVAL
        ) {
            return $this->norouteAction();
        }

        $dataToSave = array('note');
        foreach ($dataToSave as $dataKey) {
            $package->setData($dataKey,
                $this->getRequest()->getPost($dataKey)
            );
        }

        $package->setStatus(Stenik_Zeron_Model_Package_Attribute_Source_Status::STATUS_DISAPPROVED);
        $package->save();

        $order = $package->getOrder();
        $order->setState(Mage_Sales_Model_Order::STATE_PROCESSING, 'zeron_disapproved', sprintf('Отказано запитване %s', $package->getIncrementId()));
        $order->save();

        $this->loadLayout();
        $this->renderLayout();
    }

    /**
     * Init Package
     *
     * @return Stenik_Zeron_Model_Package
     */
    protected function _initPackage()
    {
        $package = Mage::getModel('stenik_zeron/package');

        $package->load($this->getRequest()->getParam('package_id'));
        if ($package->getId()) {
            Mage::register('stenik_zeron_package', $package);
        }
        return $package;
    }
}