<?php
/**
 * @package Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_Zeron_Model_CatalogInventory_Stock_Item extends Mage_CatalogInventory_Model_Stock_Item
{
    /**
     * Check quantity
     *
     * @param   decimal $qty
     * @exception Mage_Core_Exception
     * @return  bool
     */
    public function checkQty($qty)
    {
        if (!$this->getManageStock() || Mage::app()->getStore()->isAdmin()) {
            return true;
        }

        if ($this->getProduct() && Mage::helper('stenik_zeron/product')->isProductSoldByAdditionalMeasure($this->getProduct())) {
            if ($zeronMeasureQty = (float) $this->getProduct()->getZeronMeasureQty()) {
                $qty *= $zeronMeasureQty;
            }
        }

        if ($this->getQty() - $this->getMinQty() - $qty < 0) {
            switch ($this->getBackorders()) {
                case Mage_CatalogInventory_Model_Stock::BACKORDERS_YES_NONOTIFY:
                case Mage_CatalogInventory_Model_Stock::BACKORDERS_YES_NOTIFY:
                    break;
                default:
                    return false;
                    break;
            }
        }
        return true;
    }
}
