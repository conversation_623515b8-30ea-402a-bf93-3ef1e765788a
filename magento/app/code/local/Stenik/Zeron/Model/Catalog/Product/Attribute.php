<?php

/**
 * @method Stenik_Zeron_Model_Resource_Catalog_Product_Attribute getResource()
 */
class Stenik_Zeron_Model_Catalog_Product_Attribute extends Stenik_Zeron_Model_Catalog_Product_Abstract implements Stenik_Sync_Model_Interface_Product
{

    /**
     * Resource initialization
     */
    protected function _construct()
    {
        $this->_init('stenik_zeron/catalog_product_attribute');
    }

    /**
     * @param Mage_Catalog_Model_Product $product
     * @return Varien_Data_Collection
     */
    public function getItem(Mage_Catalog_Model_Product $product)
    {
        $logger = Mage::helper('stenik_zeron/log')->factory('product');
        $logger->addLogMessagePrefix('[PRODUCT] ');
        $logger->logInfo();
        $logger->logInfo('Start Inv props sync.');

        $resultCollection = new Varien_Data_Collection();

        try {
            $zeronProductCollection = $this->getResource()->getItem($product);
            $logger->logInfo('%s potential items found for "%s".', count($zeronProductCollection), $this->getMode());
            $resultCollection = $this->_parseZeronProductCollection($zeronProductCollection);
        } catch (Mage_Core_Exception $e) {
            Mage::logException($e);
            $logger->logAlert($e->getMessages());
        }

        $logger->logInfo('%s  with updated attributes ', count($resultCollection));
        $logger->removeLogMessagePrefix('[PRODUCT] ');

        return $resultCollection;
    }

    public function getCollection(Zend_Date $since = null)
    {
        $logger = Mage::helper('stenik_zeron/log')->factory('product');
        $logger->addLogMessagePrefix('[PRODUCT] ');
        $logger->logInfo();
        $logger->logInfo('Start Inv props sync.');

        $resultCollection = new Varien_Data_Collection();

        try {
            $zeronProductCollection = $this->getResource()->getItems();
            $logger->logInfo('%s potential items found for "%s".', count($zeronProductCollection), $this->getMode());
            $resultCollection = $this->_parseZeronProductCollection($zeronProductCollection);
        } catch (Mage_Core_Exception $e) {
            Mage::logException($e);
            $logger->logAlert($e->getMessages());
        }

        $logger->logInfo('%s  with updated attributes ', count($resultCollection));
        $logger->removeLogMessagePrefix('[PRODUCT] ');

        return $resultCollection;
    }

    /**
     * Parse Zeron Product Collection
     * @param  Varien_Data_Collection $zeronProductCollection
     * @return Varien_Data_Collection
     */
    protected function _parseZeronProductCollection(Varien_Data_Collection $zeronProductCollection)
    {
        $resultCollection = new Varien_Data_Collection();
        $logger = Mage::helper('stenik_zeron/log')->factory('product');

        $magentoProductSkuIdMap = $this->_getMagentoSkuIdMap();

        // Fill attributes needed for check
        $attributesToCheck = array();
        foreach ($zeronProductCollection as $zeronProductItem) {
            foreach ($zeronProductItem->getData() as $key => $value) {
                $attributesToCheck[$key] = $key;
            }
        }

        unset($attributesToCheck['id']);

        $existingAttributes = $this->fetchExistingAttributes($attributesToCheck);

        if (empty($existingAttributes)) {
            return $resultCollection;
        }
        $magentoProductIdDataMap = $this->fetchProductAttributesForProducts($existingAttributes);

        foreach ($zeronProductCollection as $zeronProductItem) {
            $magentoId = null;
            if (isset($magentoProductSkuIdMap[$zeronProductItem->getId()])) {
                $magentoId = $magentoProductSkuIdMap[$zeronProductItem->getId()];
            }

            $productExistsInMagento = (bool)$magentoId;


            $addInResult = false;
            $item = new Varien_Object();
            $item->setId($zeronProductItem->getId())
                ->setMagentoId($magentoId)
                ->setMagentoSku($zeronProductItem->getId());

            foreach ($zeronProductItem->getData() as $key => $value) {
                $item->setData($key, $value);
            }

            // Check if update is required
            if ($productExistsInMagento && isset($magentoProductIdDataMap[$magentoId])) {
                $magentoProductData = $magentoProductIdDataMap[$magentoId];
                foreach ($attributesToCheck as $attributeCode) {
                    $match = !key_exists($attributeCode, $magentoProductData) || $magentoProductData[$attributeCode] == $item->getData($attributeCode);

                    if (!$match) {
                        $addInResult = true;
                    } else {
//                        $item->unsetData($attributeCode);
                    }
                }

            }

            if ($addInResult) {
                try {
                    $resultCollection->addItem($item);
                } catch (Exception $e) {
                    $logger->logError($e->getMessages());
                }
            }
        }

        return $resultCollection;
    }

    /**
     * @param $attributes
     * @return array|mixed
     */
    protected function fetchProductAttributesForProducts($attributes)
    {
        $productCollection = Mage::getModel('catalog/product')->getCollection();
        $adapter = $productCollection->getConnection();

        $productCollection->getSelect()->reset(Zend_Db_Select::COLUMNS);
        $productCollection->getSelect()->columns(array('entity_id'));
        $productCollection->addAttributeToSelect($attributes, 'left');

        $this->_magentoProductIdDataMap = $adapter->fetchAssoc($productCollection->getSelect());

        return $this->_magentoProductIdDataMap;
    }

    /**
     * @param $attributes
     * @return array
     */
    protected function fetchExistingAttributes($attributes)
    {
        $entity = 'catalog_product';

        $existingAttributes = array();
        foreach ($attributes as $attrCode) {
            $attr = Mage::getResourceModel('catalog/eav_attribute')
                ->loadByCode($entity, $attrCode);
            if ($attr->getId()) {
                $existingAttributes[] = $attrCode;
            }
        }

        return $existingAttributes;
    }


}