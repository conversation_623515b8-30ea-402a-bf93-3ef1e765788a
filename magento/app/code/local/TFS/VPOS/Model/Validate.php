<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 *
 * @category   Payment throw TFS Virtual POS
 * @package    TFS_VPOS
 * <AUTHOR>
 * @copyright  Copyright (c) 2015  TFS JSC
 * @copyright  Copyright (c) 2015  (http://www.transcard.bg)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */


class TFS_VPOS_Model_Validate extends Mage_Core_Model_Config_Data {


    public function save() {
        
        $error_msg = '';
        
        $module_status = $this->getFieldsetDataValue('active');
        if($module_status == 1){
        // go to validate fields
            $trader_number = $this->getFieldsetDataValue('trader_number');
            $order_status = $this->getFieldsetDataValue('order_status');
            $order_status_after_payment = $this->getFieldsetDataValue('order_status_after_payment');
            $priv_key = $this->getFieldsetDataValue('priv_key');
            $tfs_cert = $this->getFieldsetDataValue('tfs_cert');
            
            
            if(empty($trader_number))
                $error_msg = __('Please fill in Trader TFS Number <br> ');
            else if(!preg_match('/^[0-9]*$/', $trader_number))
                $error_msg .= __('Trader TFS Number can be only digits').'<br>';
                
            if(empty($order_status))
                $error_msg .= __("Please select option for  'Order status (Before the payment is made)'")."<br>";
            if(empty($order_status_after_payment))
                $error_msg .= __("Please select option for 'New order status (When the payment is made)'")."<br>";
                
            
            if(!file_exists(Mage::getBaseDir('var')."/tfs/certs/".Mage::getModel('vpos/paymentMethod')->getConfigData('priv_key'))) {
            
                if(empty($priv_key))
                    $error_msg .= __("Please upload your private key in field 'Private key'. This file come from TFS.") . "<br>";
            }
        
            
            if(!file_exists(Mage::getBaseDir('var')."/tfs/certs/".Mage::getModel('vpos/paymentMethod')->getConfigData('tfs_cert'))) {
                if(empty($tfs_cert))
                    $error_msg .= __("Please upload public certificate of TFS in field 'TFS's Public certificate'. This file come from TFS.") . "<br>";
            }        
        }
        
        if(!empty($error_msg)) {
            //there are fields for correct 
            Mage::throwException("$error_msg"); 
        } else {
            return parent::save();  //call original save method so whatever happened
        }
    }
}