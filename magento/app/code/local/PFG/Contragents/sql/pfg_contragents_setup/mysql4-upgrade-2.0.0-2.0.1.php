<?php
/** @var Mage_Core_Model_Resource_Setup $installer */
$installer = $this;
$connection = $installer->getConnection();
$installer->startSetup();

$contragentsTableName = Mage::getSingleton('core/resource')
    ->getTableName('pfg_contragents/contragents');

$connection->addIndex(
    $contragentsTableName,
    "contragent_partner_code_unique",
    ['partner_code'],
    Varien_Db_Adapter_Interface::INDEX_TYPE_UNIQUE
);

$installer->endSetup();
