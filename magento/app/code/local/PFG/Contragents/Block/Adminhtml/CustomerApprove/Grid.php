<?php

class PFG_Contragents_Block_Adminhtml_CustomerApprove_Grid extends Mage_Adminhtml_Block_Widget_Grid
{
    protected function _construct()
    {
        parent::_construct();

        $this->setId('customerapproveGrid');
        $this->setUseAjax(true);
        $this->setDefaultSort('entity_id');
        $this->setSaveParametersInSession(false);
    }

    protected function _prepareCollection()
    {
        if (Mage::helper('pfg_contragents')->allowOnlyApprovedCustomerLoggin()) {
            $this->setCollection(Mage::getModel('customer/customer')->getNotApprovedCustomerCollection());
        } else {
            $this->setCollection(new Varien_Data_Collection());
        }

        return parent::_prepareCollection();
    }


    protected function _prepareColumns()
    {
        $this->addColumn('entity_id', [
            'header' => $this->__('Customer ID'),
            'index' => 'entity_id',
        ]);

        $this->addColumn('name', [
            'header' => $this->__('Customer Name'),
            'index' => 'name',
        ]);

        $this->addColumn('email', [
            'header' => $this->__('Email'),
            'index' => 'email',
        ]);

        $this->addColumn('created_at', [
            'header' => $this->__('Created At'),
            'index' => 'created_at',
        ]);

        return parent::_prepareColumns();
    }

    /**
     * @param Mage_Customer_Model_Customer $row
     * @return string
     */
    public function getRowUrl($item): string
    {
        return $this->getUrl('*/*/edit', ['id' => $item->getId()]);
    }
}
