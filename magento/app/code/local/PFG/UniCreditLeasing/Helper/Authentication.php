<?php

class PFG_UniCreditLeasing_Helper_Authentication extends Mage_Core_Helper_Abstract
{
    /**
     * @param $formData array|stdClass
     * @return array|stdClass
     */
    public function addStoreCredentialsToFormData($formData)
    {
        if (!is_array($formData) && !is_object($formData)) {
            return $formData;
        }

        if (is_array($formData)) {
            $formData['user'] = $this->getUnicreditLeasingUser();
            $formData['pass'] = $this->getUnicreditLeasingPassword();
        } elseif (is_object($formData)) {
            $formData->user = $this->getUnicreditLeasingUser();
            $formData->pass = $this->getUnicreditLeasingPassword();
        }

        return $formData;
    }

    /**
     * @return string
     */
    protected function getUnicreditLeasingUser()
    {
        return (string)Mage::getStoreConfig('payment/unicredit_leasing/username');
    }

    /**
     * @return string
     */
    protected function getUnicreditLeasingPassword()
    {
        return (string)Mage::getStoreConfig('payment/unicredit_leasing/password');
    }
}