<?php

class PFG_UniCreditLeasing_Helper_Request_Coef extends PFG_UniCreditLeasing_Helper_Request
{
    const REQUEST_URI = 'getCoeff';

    public function getPossibleInstallments($totalPrice, $productCode)
    {
        if ($totalPrice < 0) {
            $totalPrice = 0;
        }
        $cacheId = $this->calculateCacheKey($totalPrice, $productCode);
        $coeffValuesCalculated = $this->usesCache() ?
            unserialize(Mage::helper('core')->_loadCache($cacheId))
            : false;
        if (!$coeffValuesCalculated) {
            $coeffValues = $this->getCoeffValues($productCode);
            $coeffValuesCalculated = [];
            foreach ($coeffValues as $coeffIndex => $coeffData) {
                if (!isset($coeffData['installment_coefficient']) || !$coeffData['installment_coefficient']) {
                    continue;
                }

                $coeffValuesCalculated[$coeffIndex] = $coeffData;
                $coeffValuesCalculated[$coeffIndex]['monthly_installment'] = number_format(
                    round($coeffData['installment_coefficient'] * $totalPrice, 2), 2
                );
            }

            if ($this->usesCache()) {
                $this->_saveCache(serialize($coeffValuesCalculated), $cacheId, $this->getCacheTags());
            }
        }

        return $coeffValuesCalculated;
    }

    public function getCoeffValues($productCode = '', $installmentsCount = 0)
    {
        $params = ['onlineProductCode' => $productCode];
        if ($installmentsCount) {
            $params['installmentCount'] = $installmentsCount;
        }
        return $this->sendPost('getCoeff', $params);
    }

    protected function performRequest($params = [])
    {
        return $this->sendPost(self::REQUEST_URI, $params, []);
    }

    /**
     * @param \Psr\Http\Message\ResponseInterface $response
     * @return array|mixed
     * @throws Mage_Core_Exception
     */
    protected function processResponse($response)
    {
        $responseJson = parent::processResponse($response);
        if (!isset($responseJson['coeffList']) || !$responseJson['coeffList']) {
            Mage::throwException($this->__('Invalid getCoeff response. CoeffList is missing.'));
        }

        $coeffList = $responseJson['coeffList'];
        if (!is_array($coeffList) || empty($coeffList)) {
            Mage::throwException($this->__('Invalid getCoeff response. CoeffList is empty or malformed.'));
        }

        $coeffData = [];
        foreach ($coeffList as $singleCoeff) {
            $coeffData[] = [
                'online_product_code' => $singleCoeff['onlineProductCode'] ?? false,
                'installments_count' => $singleCoeff['installmentCount'] ?? false,
                'installment_coefficient' => $singleCoeff['coeff'] ?? false,
                'interest_percent' => $singleCoeff['interestPercent'] ?? false,
            ];
        }

        return $coeffData;
    }

    protected function calculateCacheKey($totalPrice, $productCode)
    {
        $cacheKey = (string)($totalPrice * 1000) . '-' . $productCode;
        return md5($cacheKey);
    }

    public function usesCache()
    {
        return false;
    }

    protected function getCacheTags()
    {
        return [
            Mage_Core_Block_Abstract::CACHE_GROUP,
            Mage_Core_Model_Config::CACHE_TAG,
        ];
    }
}
