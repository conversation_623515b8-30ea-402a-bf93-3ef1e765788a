<?php
class PFG_Widget_Helper_Data extends Mage_Core_Helper_Abstract {

    public function resizeImg($fileName, $width, $height = '', $retina = false)
    {
        $folderURL = Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_MEDIA);
        $imageURL = $folderURL . $fileName;
	
	    $basePath = Mage::getBaseDir(Mage_Core_Model_Store::URL_TYPE_MEDIA) . "/wysiwyg/" . $fileName;
	
	    if ($retina) {
		    $newPath = Mage::getBaseDir(Mage_Core_Model_Store::URL_TYPE_MEDIA) . "/wysiwyg/resized/retina/" . $fileName;
	    } else {
		    $newPath = Mage::getBaseDir(Mage_Core_Model_Store::URL_TYPE_MEDIA) . "/wysiwyg/resized/" . $fileName;
	    }
	
        //if width empty then return original size image's URL
        if ($width != '') {
            //if image has already resized then just return URL
            if (file_exists($basePath) && is_file($basePath) && !file_exists($newPath)) {
                $imageObj = new Varien_Image($basePath);
                $imageObj->constrainOnly(TRUE);
                $imageObj->keepAspectRatio(FALSE);
                $imageObj->keepFrame(FALSE);
                $imageObj->resize($width, $height);
                $imageObj->save($newPath);
            }
	        if ($retina) {
		        $resizedURL = Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_MEDIA)  . "/wysiwyg/resized/retina/". $fileName;
	        } else {
		        $resizedURL = Mage::getBaseUrl(Mage_Core_Model_Store::URL_TYPE_MEDIA)  . "/wysiwyg/resized/". $fileName;
	        }
        } else {
            $resizedURL = $imageURL;
        }
        return $resizedURL;
    }
}