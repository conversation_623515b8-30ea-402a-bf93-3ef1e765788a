<?xml version="1.0"?>
<config>
	<modules>
		<PFG_Lazyblocks>
			<version>0.1.0</version>
		</PFG_Lazyblocks>
	</modules>
	<global>
		<models>
			<pfg_lazyblocks>
				<class>PFG_Lazyblocks_Model</class>
			</pfg_lazyblocks>
		</models>
		<blocks>
			<pfg_lazyblocks>
				<class>PFG_Lazyblocks_Block</class>
			</pfg_lazyblocks>
		</blocks>
		<helpers>
			<pfg_lazyblocks>
				<class>PFG_Lazyblocks_Helper</class>
			</pfg_lazyblocks>
		</helpers>
	</global>
	<frontend>
		<layout>
			<updates>
				<pfg_lazyblocks>
					<file>pfg/lazyblocks.xml</file>
				</pfg_lazyblocks>
			</updates>
		</layout>
		<routers>
			<lazyblock>
				<use>standard</use>
				<args>
					<frontName>lazyblock</frontName>
					<module>PFG_Lazyblocks</module>
				</args>
			</lazyblock>
		</routers>
	</frontend>
</config>
