<?php

/* @var $installer Mage_Core_Model_Resource_Setup */
$installer = $this;

$installer->startSetup();

$table = $installer->getTable('extensa_econt/office');
$coordinatesColumn = 'coordinates';

if (!$installer->getConnection()->tableColumnExists($installer->getTable($table), $coordinatesColumn)) {
    $installer->getConnection()
        ->addColumn($table, $coordinatesColumn, [
            'type' => Varien_Db_Ddl_Table::TYPE_TEXT,
            'nullable' => false,
            'length' => 255,
            'after' => null,
            'comment' => 'Office coordinates'
        ]);
}

$installer->endSetup();