<?php

class PFG_Theme_Customer_AddressController extends PFG_Theme_Controller_AbstractController
{
    public function createAddressAction()
    {
        try {
            $data = $this->getBody();
            $newAddressData = $data['data'] ?? [];
            $customer = $this->_getCustomer($data);
            $address = $this->_helper()->getCustomerAddress(
                $customer,
                0,
                $newAddressData,
            );

            $address->save();

            $defaultBilling = $newAddressData['isDefaultBilling'] ?? 0;
            $defaultShipping = $newAddressData['isDefaultShipping'] ?? 0;
            $save = false;
            if ($defaultShipping) {
                $customer->setDefaultShipping($address->getId());
                $save = true;
            }

            if ($defaultBilling) {
                $customer->setDefaultBilling($address->getId());
                $save = true;
            }

            if ($save) {
                $customer->save();
            }

            $this->_sendOk([
                'customer_id' => $customer->getId(),
                'address_id' => $address->getId(),
                'success' => true,
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function updateAddressAction()
    {
        try {
            $data = $this->getBody();
            $newAddressData = $data['data'] ?? [];
            $addressId = $data['address_id'] ?? 0;
            $customer = $this->_getCustomer($data);

            if (!$addressId) {
                throw new Exception('Address not found');
            }

            $address = $this->_helper()->getCustomerAddress(
                $customer,
                $addressId,
                $newAddressData,
            );

            $address->save();

            $defaultBilling = $newAddressData['isDefaultBilling'] ?? 0;
            $defaultShipping = $newAddressData['isDefaultShipping'] ?? 0;
            $save = false;
            if ($defaultShipping) {
                $customer->setDefaultShipping($address->getId());
                $save = true;
            }

            if ($defaultBilling) {
                $customer->setDefaultBilling($address->getId());
                $save = true;
            }

            if ($save) {
                $customer->save();
            }

            $this->_sendOk([
                'customer_id' => $customer->getId(),
                'address_id' => $address->getId(),
                'success' => true,
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function deleteAddressAction()
    {
        try {
            $data = $this->getBody();
            $addressId = $data['address_id'] ?? 0;
            $customer = $this->_getCustomer($data);

            /** @var Mage_Customer_Model_Address $address */
            $address = Mage::getModel('customer/address')->load($addressId);
            if (!$address->getId()) {
                throw new Exception("Няма адрес със ИД: " . $addressId);
            } else if (!$address->getCustomerId() || $address->getCustomerId() != $customer->getId()) {
                throw new Exception("Адрес {$addressId} не принадлежи на клинет {$customer->getId()}");
            }

            $address->delete();

            $this->_sendOk([
                'customer_id' => $customer->getId(),
                'success' => true,
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }


    /**
     * @throws Exception
     */
    public function _getCustomer(array $data): Mage_Customer_Model_Customer
    {
        $customer = Mage::getModel('customer/customer')->load($data["id"] ?? 0);
        if (!$customer || !$customer->getId()) {
            throw new Exception('Customer not found', 404);
        } else if (empty($data)) {
            throw new Exception('Empty update data', 400);
        }

        return $customer;
    }

    private function _helper(): PFG_Theme_Helper_Customer
    {
        return Mage::helper('pfg_theme/customer');
    }
}
