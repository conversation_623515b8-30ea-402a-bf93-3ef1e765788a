<?php

class PFG_Theme_Store_ContentController extends PFG_Theme_Controller_AbstractController
{
    public function getPageAction()
    {
        try {
            $id = $this->getBody()['id'] ?? 0;
            if ($id < 1) {
                throw new Exception('Невалидна cms страница');
            }

            $cmsPage = Mage::getModel('cms/page')->load((int)$id);
            if (!$cmsPage->getId()) {
                throw new Exception('Невалидна cms страница');
            }

            $processor = $this->_cmsHelper()->getPageTemplateProcessor();
            $html = $processor->filter($cmsPage->getContent());

            $this->_sendOk([
                'content' => $html,
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function getBlockAction()
    {
        try {
            $data = $this->getBody();
            $id = $data['id'] ?? 0;
            if ($id < 1) {
                throw new Exception('Невалиден cms блок');
            }
            $cmsBlock = Mage::getModel('cms/block')->load((int)$id);
            if (!$cmsBlock->getId()) {
                throw new Exception('Невалиден cms блок');
            }

            $processor = $this->_cmsHelper()->getBlockTemplateProcessor();
            $html = $processor->filter($cmsBlock->getContent());

            $this->_sendOk([
                'content' => $html,
            ]);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    private function _cmsHelper(): Mage_Cms_Helper_Data
    {
        return Mage::helper('cms');
    }
}
