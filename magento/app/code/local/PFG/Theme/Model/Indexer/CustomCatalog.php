<?php

class PFG_Theme_Model_Indexer_CustomCatalog extends PFG_Theme_Model_Indexer_AbstractIndex
{
    const INDEX_CODE = 'custom_catalog';
    const TABLE_CONFIG_PATH = 'pfg_theme/catalog/table_name';
    const INDEX_TABLE_CACHE_KEY = 'graph_api:str[1]:cnf:pfg_theme/catalog/table_name';

    private int $_pageSize = 10000;
    private int $_bkpTablesCount = 3;

    private string $_tablePrefix = 'praktis_flat_';

    // ########################################
    // remove this attributes because they are combined by
    // $this->_getProductFreeShipping($productData);
    // into Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD
    // ########################################
    // status is only used to check if product should be included in index
    // no need to store the value in the table
    protected array $_notInIndexTableAttributes = [
        Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_ATTRIBUTE,
        Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_START_DATE,
        Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_END_DATE,
        'status',
    ];

    public function getName(): string
    {
        return 'Custom Catalog Indexer';
    }

    public function getDescription(): string
    {
        return "Custom Index for better category performance";
    }

    /**
     * @return void
     * @throws Exception
     */
    public function reindexAll(): void
    {
        try {
            // set memory limit
            ini_set('memory_limit', '2048M');
            $startTime = microtime(true);
            $table = $this->_getIndexTableName();
            $this->_log("Reindexing all: $table");

            $attributeCodes = $this->_getHelper()->getAttributesForTable();

            $tmpTable = "tmp_$table";
            $this->_createIndexTable($tmpTable, $attributeCodes);
            $this->_indexProducts($tmpTable, $attributeCodes);

            $this->_getConnection()->query("ALTER TABLE $tmpTable RENAME TO $table");

            $this->_updateCache($table);

            $this->_clearOldIndexTable();

            $endTime = time();

            $this->_log("Peek memory usage: " . memory_get_peak_usage());
            $this->_log("Done");
            $this->_log("Elapsed time: " . ($endTime - $startTime));

            $this->_updateIndexStatus($startTime, $endTime);
        } catch (Throwable $e) {
            Mage::logException($e);
            $this->_log("ERROR: " . $e->getMessage());
        }
    }

    protected function _registerEvent(Mage_Index_Model_Event $event): void
    {
    }

    protected function _processEvent(Mage_Index_Model_Event $event): void
    {
    }

    private function _clearOldIndexTable(): void
    {
        $tables = $this->_getConnection()->fetchCol("show tables like '%tmp_theme_flat_catalog%'");
        foreach ($tables as $table) {
            $this->_getConnection()->dropTable($table);
        }

        $this->_log("Removing old index table");
        // get tables that start with show tables like '%theme_catalog_flat%'
        $tables = $this->_getConnection()->fetchCol("show tables like '%theme_flat_catalog_%'");
        if (count($tables) > $this->_bkpTablesCount) {
            sort($tables);
            $tables = array_slice($tables, 0, count($tables) - $this->_bkpTablesCount);
            foreach ($tables as $table) {
                $this->_getConnection()->dropTable($table);
            }
        }
    }

    /**
     * @throws Exception
     */
    private function _indexProducts(string $table, array $attrs): void
    {
        $attributeMetaData = $this->_getAttributeMetaData($attrs);
        $availableAttributes = [];
        foreach ($attrs as $code) {
            foreach ($attributeMetaData as $code2 => $attribute) {
                if ($attribute['attribute_code'] === $code) {
                    $availableAttributes[] = $code;
                } else if (in_array($code2, ['attribute_set_id','qty'])) {
                    $availableAttributes[] = $code2;
                }
            }
        }

        $availableAttributes = array_unique(array_filter($availableAttributes, function ($code) {
            return !in_array($code, $this->_notInIndexTableAttributes);
        }));

        // separate attributes by backend type
        $attributesByBackendType = [];
        $urlKeyAttributeId = null;
        foreach ($attributeMetaData as $attribute) {
            $beType = $attribute['backend_type'] ?? '';
            if (!$beType) {
                continue;
            }

            if ($attribute['attribute_code'] == Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD) {
                // skip because data is retrieved from SHIPPING_DISCOUNT_ATTRIBUTE
                continue;
            }

            if ($attribute['attribute_code'] === 'url_key') {
                $urlKeyAttributeId = $attribute['attribute_id'];
            }

            $attributesByBackendType[$beType][$attribute['attribute_id']] = $attribute['attribute_code'];
        }

        $this->_log("Indexing products");
        $page = 1;
        $count = 0;
        $now = new DateTime();
        while (true) {
            $this->_log("Reindexing page $page");
            $start = microtime(true);
            $offset = ($page - 1) * $this->_pageSize;
            $data = $this->_getConnection()->fetchAll("select entity_id, sku, attribute_set_id, created_at from catalog_product_entity limit $this->_pageSize offset $offset");

            if (empty($data)) {
                $this->_log("No more products to index");
                break;
            }

            $productsData = [];
            $ids = [];
            foreach ($data as $row) {
                $id = $row['entity_id'];
                $productsData[$id] = [
                    'entity_id'        => $id,
                    'sku'              => $row['sku'],
                    'attribute_set_id' => $row['attribute_set_id'],
                    'created_at'       => $row['created_at'],
                    'qty'              => 0
                ];
                $ids[] = $id;
            }

            // Skip processing if no products found
            if (empty($ids)) {
                $page++;
                continue;
            }

            $productsIds = implode(", ", $ids);

            // Fetch URL rewrites
            $urls = $this->_getConnection()->fetchPairs("select product_id, request_path from core_url_rewrite where category_id is null and target_path like 'catalog/product/view/id/%' and product_id in ($productsIds)");

            // Process attributes by backend type
            foreach ($attributesByBackendType as $type => $attributes) {
                if (empty($attributes)) {
                    continue;
                }

                $attrTable = '';
                $attributeIds = implode(',', array_keys($attributes));

                if ($type === 'static') {
                    continue;
                } else if ($type === 'int') {
                    $attrTable = 'catalog_product_entity_int';
                } else if ($type === 'varchar') {
                    $attrTable = 'catalog_product_entity_varchar';
                } else if ($type === 'decimal') {
                    $attrTable = 'catalog_product_entity_decimal';
                } else if ($type === 'datetime') {
                    $attrTable = 'catalog_product_entity_datetime';
                } else {
                    throw new Exception("Unknown backend type: $type -> " . json_encode($attributeIds));
                }

                $data = $this->_getConnection()->fetchAll("select entity_id, attribute_id, value from $attrTable where entity_id in ($productsIds) and attribute_id in ($attributeIds) and store_id = 0;");

                foreach ($data as $row) {
                    $value = $row['value'];
                    if ($row['attribute_id'] === $urlKeyAttributeId && isset($urls[$row['entity_id']])) {
                        $value = $urls[$row['entity_id']];
                    }

                    $productsData[$row['entity_id']][$attributes[$row['attribute_id']]] = $value;
                }
            }

            // Get inventory data
            $qtyData = $this->_getConnection()->fetchPairs("select product_id, qty from cataloginventory_stock_item s where product_id in ($productsIds) and qty > 0;");

            // Get price data
            $priceData = $this->getPricesMap($productsIds);

            $this->_log("Loaded products in " . (microtime(true) - $start) . " seconds");
            $start = microtime(true);
            $insertData = [];

            foreach ($productsData as $id => $productData) {
                // Skip products with inactive status
                if (!isset($productData['status']) || $productData['status'] != 1) {
                    continue;
                } else {
                    unset($productData['status']);
                }

                $rowData = [$id];

                // Process price data
                if (isset($priceData[$id])) {
                    $price = $priceData[$id]['price'];
                    $finalPrice = $priceData[$id]['final_price'];

                    $productData['price'] = $price;
                    if ($price > $finalPrice) {
                        $productData['special_price'] = $finalPrice;
                    } else {
                        $productData['special_price'] = null;
                    }
                    $productData['special_from_date'] = null;
                    $productData['special_to_date'] = null;
                } else {
                    if (isset($productData['special_price']) && $productData['special_price'] !== null) {
                        // Check if special price is expired
                        $specialPriceToDate = $productData['special_to_date'] ?? null;
                        $specialPriceFromDate = $productData['special_from_date'] ?? null;
                        if ($specialPriceToDate && $specialPriceToDate < $now->format('Y-m-d H:i:s')) {
                            $productData['special_price'] = null;
                            $productData['special_from_date'] = null;
                            $productData['special_to_date'] = null;
                        } else if ($specialPriceFromDate && $specialPriceFromDate > $now->format('Y-m-d H:i:s')) {
                            $productData['special_price'] = null;
                            $productData['special_from_date'] = null;
                            $productData['special_to_date'] = null;
                        }
                    }
                }

                $productData = $this->_getProductFreeShipping($productData);

                foreach ($availableAttributes as $code) {
                    if ($code === 'qty') {
                        $rowData[] = $qtyData[$id] ?? 0;
                    } else {
                        $rowData[] = $productData[$code] ?? null;
                    }
                }

                $insertData[] = $rowData;
            }

            // Skip if no data to insert
            if (empty($insertData)) {
                $this->_log("No data to insert for page $page");
                $page++;
                continue;
            }

            $this->_log("Prepared data in " . (microtime(true) - $start) . " seconds");

            $start = microtime(true);
            $this->_getConnection()->insertArray($table, ['entity_id', ...$availableAttributes], $insertData);
            $this->_log("Inserted data in " . (microtime(true) - $start) . " seconds");

            $count += count($insertData);
            $page++;

            if (count($data) < $this->_pageSize) {
                $this->_log("Done reindexing products: $count");
                break;
            }
        }
    }

    private function _updateIndexStatus(float $startTime, int $now): void
    {
        $this->_getConnection()
            ->query("update index_process set 
        status = 'pending', 
        started_at = '" . date('Y-m-d H:i:s', $startTime) . "',
        ended_at = '" . date('Y-m-d H:i:s', $now) . "'
where indexer_code = 'theme_catalog'");
    }

    private function _createIndexTable(string $table, array $attributeCodes)
    {
        $attributeMetaData = $this->_getAttributeMetaData($attributeCodes);
        $createSql = $this->_getCreateTableSql($table, $attributeMetaData);

        $this->_log("Creating table $table");
        $this->_log($createSql);

        $this->_getConnection()->query($createSql);
    }

    private function _getCreateTableSql(string $table, array $attributeMetaData): string
    {
        $columns = array_map(function ($attribute) {
            if (in_array($attribute['attribute_code'], $this->_notInIndexTableAttributes)) {
                return "";
            }

            $beType = $attribute['backend_type'] ?? '';
            if ($beType === 'decimal') {
                return "`{$attribute['attribute_code']}` decimal(12,4) default null";
            } else if ($beType === 'datetime') {
                return "`{$attribute['attribute_code']}` datetime default null";
            } else if ($beType === 'int') {
                return "`{$attribute['attribute_code']}` int default null";
            } else if ($beType === 'varchar') {
                return "`{$attribute['attribute_code']}` varchar(255) default null";
            } else if ($beType === 'static') {
                return "`{$attribute['attribute_code']}` varchar(30) default null";
            }

            return "";
        }, $attributeMetaData);

        $columns = array_filter($columns);

        return sprintf("CREATE TABLE IF NOT EXISTS `$table` (
    `entity_id` int unsigned NOT NULL,
    %s,
    `attribute_set_id` int unsigned NOT NULL,
    `qty` int unsigned NOT NULL,
    PRIMARY KEY (entity_id),
    UNIQUE KEY `product_sku` (`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
", implode(", \n", $columns));
    }

    private function _getIndexTableName(): string
    {
        return $this->_getHelper()->getCatalogTableName();
    }

    protected $_allowAttributeTypes = [
        'static',
        'int',
        'varchar',
        'decimal',
        'datetime'
    ];

    private function _getAttributeMetaData(array $codes): array
    {
        $codes = array_filter($codes, function ($code) {
            return $code !== Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD;
        });

        $result = array_fill_keys($codes, []);
        $data = $this->_getConnection()->fetchAll(str_replace(":codes", implode("', '", $codes), "select
    a.attribute_id,
    a.attribute_code,
    a.backend_type
from eav_attribute a
    inner join catalog_eav_attribute cea on a.attribute_id = cea.attribute_id
where a.attribute_code in (':codes') and a.entity_type_id = {$this->_getProductEntityTypeId()};"));

        foreach ($data as $row) {
            $type = $row['backend_type'];
            if (!in_array($type, $this->_allowAttributeTypes)) {
                continue;
            }

            $result[$row['attribute_code']] = [
                'attribute_id'   => $row['attribute_id'],
                'attribute_code' => $row['attribute_code'],
                'backend_type'   => $row['backend_type'],
            ];
        }

        $result[Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD] = [
            'attribute_id'   => 0,
            'attribute_code' => Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD,
            'backend_type'   => 'int',
        ];

        return $result;
    }

    private function _getProductEntityTypeId(): int
    {
        return Mage::getModel('eav/entity')
            ->setType('catalog_product')
            ->getTypeId();
    }

    private function _updateCache(string $table): void
    {
        /** @var Mage_Core_Model_Config $config */
        $config = Mage::getModel('core/config');
        $config->saveConfig(self::TABLE_CONFIG_PATH, $table, 'default', 0);
        $be = $this->_getGraphqlCache();
        $be->setValue(self::INDEX_TABLE_CACHE_KEY, $table);
    }

    private function _getGraphqlCache(): PFG_Theme_Model_Cache_Graphql
    {
        return Mage::getModel('pfg_theme/cache_graphql');
    }

    private function _getProductFreeShipping(array $productData): array
    {
        if ($productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_ATTRIBUTE] == 1) {
            $startDate = $productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_START_DATE];
            $endDate = $productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_END_DATE];

            if ($startDate != '' || $endDate != '') {
                $startDate = new Zend_Date($startDate, 'yyyy-MM-dd');
                $endDate = new Zend_Date($endDate, 'yyyy-MM-dd');

                $today = new DateTime();
                $today->setTimestamp(time());
                $today->setTime(0, 0, 0);
                $today = new Zend_Date($today->format('Y-m-d, H:i:s'), 'yyyy-MM-dd');

                if ($startDate->compare($endDate) < 1
                    && $startDate->compare($today) < 1
                    && $endDate->compare($today) >= 0) {
                    $productData[Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD] = 1;
                }
            }
        } else {
            $productData[Praktis_ShippingDiscount_Helper_Data::CUSTOM_INDEX_FIELD] = 0;
        }
        unset($productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_ATTRIBUTE]);
        unset($productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_START_DATE]);
        unset($productData[Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_END_DATE]);

        return $productData;
    }

    private function getPricesMap(string $productsIds): array
    {
        $defaultWebsiteId = Mage::app()->getDefaultStoreView()->getWebsiteId();
        $data = $this->_getConnection()->fetchAll("select entity_id, price, final_price 
from catalog_product_index_price p where 
    customer_group_id = 0 and 
    entity_id in ({$productsIds}) and 
    website_id = {$defaultWebsiteId};
");

        $result = [];
        foreach ($data as $row) {
            $id = (int)$row['entity_id'];
            $result[$id] = [
                'price' => $row['price'],
                'final_price' => $row['final_price'],
            ];
        }

        return $result;
    }


}
