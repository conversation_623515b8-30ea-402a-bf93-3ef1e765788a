<?xml version="1.0"?>
<config>
    <modules>
        <PFG_CatalogImages>
            <version>0.1.2</version>
        </PFG_CatalogImages>
    </modules>
    <global>
        <models>
            <pfg_catalogimages>
                <class>PFG_CatalogImages_Model</class>
                <resourceModel>pfg_catalogimages_resource</resourceModel>
            </pfg_catalogimages>
            <pfg_catalogimages_resource>
                <class>PFG_CatalogImages_Model_Resource</class>
                <entities>
                    <catalogimage>
                        <table>pfg_catalogimages_catalogimage</table>
                    </catalogimage>
                </entities>
            </pfg_catalogimages_resource>
        </models>
        <blocks>
            <pfg_catalogimages>
                <class>PFG_CatalogImages_Block</class>
            </pfg_catalogimages>
        </blocks>
        <helpers>
            <pfg_catalogimages>
                <class>PFG_CatalogImages_Helper</class>
            </pfg_catalogimages>
        </helpers>
        <resources>
            <pfg_catalogimages_setup>
                <setup>
                    <module>PFG_CatalogImages</module>
                </setup>
            </pfg_catalogimages_setup>
        </resources>
    </global>

    <admin>
        <routers>
            <pfg_catalogimages>
                <use>admin</use>
                <args>
                    <module>PFG_CatalogImages</module>
                    <frontName>pfg_catalogimages</frontName>
                </args>
            </pfg_catalogimages>
        </routers>
    </admin>

    <adminhtml>
        <menu>
            <catalog>
                <children>
                    <pfg_catalogimages module="pfg_catalogimages">
                        <title>Catalog Images</title>
                        <sort_order>150</sort_order>
                        <action>pfg_catalogimages/adminhtml_image</action>
                    </pfg_catalogimages>
                </children>
            </catalog>
        </menu>
        <acl>
            <resources>
                <all>
                    <title>Allow Everything</title>
                </all>
                <admin>
                    <children>
                        <catalog>
                            <children>
                                <pfg_catalogimages module="pfg_catalogimages">
                                    <title>Catalog Images</title>
                                    <sort_order>150</sort_order>
                                </pfg_catalogimages>
                            </children>
                        </catalog>
                    </children>
                </admin>
            </resources>
        </acl>
        <layout>
            <updates>
                <pfg_catalogimages>
                    <file>pfg/catalog_images.xml</file>
                </pfg_catalogimages>
            </updates>
        </layout>
    </adminhtml>
</config>