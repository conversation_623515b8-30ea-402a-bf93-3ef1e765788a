<?php

class PFG_Zeron_Model_Api_Warehouses extends PFG_Zeron_Model_Api_Abstract
{
    /**
     * @return SimpleXMLElement
     * @throws Mage_Core_Exception
     */
    public function getWarehouses(): SimpleXMLElement
    {
        return $this->performOperationCall('', 'GetStorehouses');
    }

    /**
     * @return array
     * @throws Mage_Core_Exception
     */
    public function getWarehousesAsArray(): array
    {
        $warehousesXml = $this->getWarehouses();

        if (!isset($warehousesXml->Destination->Operation->DataSet->Table)) {
            Mage::throwException(Mage::helper('pfgzeron')->__('No dataset in response XML'));
        }

        if ($warehousesXml->Destination->Operation->DataSet->Table->count() <= 0) {
            Mage::throwException(Mage::helper('pfgzeron')->__('No partners in response XML dataset'));
        }

        $results = [];
        foreach ($warehousesXml->Destination->Operation->DataSet->Table as $singleDataSet) {
            $tempResult = [];
            foreach ($singleDataSet as $dataSetKey => $dataSetValue) {
                $tempResult[$dataSetKey] = (string)$dataSetValue;
            }
            $results[] = $tempResult;
        }

        return $results;
    }
}
