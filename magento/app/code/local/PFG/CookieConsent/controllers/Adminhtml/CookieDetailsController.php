<?php

class PFG_CookieConsent_Adminhtml_CookieDetailsController extends PFG_CookieConsent_Controller_AbstractController
{
    public function getModel(): PFG_CookieConsent_Model_CookieDetails
    {
        return Mage::getModel('pfg_cookieconsent/cookieDetails');
    }

    public function getFormBlock(): PFG_CookieConsent_Block_Adminhtml_CookieDetails_Edit
    {
        return $this->getLayout()->createBlock('pfg_cookieconsent/adminhtml_cookieDetails_edit');
    }

    public function saveAction()
    {
        try {
            $cookieDetails = $this->saveModel();
            $this->_getSession()->setFormData(false);
            $this->_getSession()->addSuccess($this->__('The Cookie Details has been saved.'));

            if ($this->getRequest()->getParam('back', false)) {
                $this->_redirect('*/*/edit', ['id' => $cookieDetails->getId()]);
                return;
            }

            $this->_redirect('*/cookieVendors/edit', ['id' => $cookieDetails->getVendorId()]);
        } catch (Exception $e) {
            $this->_getSession()->addError($e->getMessage());
            Mage::logException($e);
            $this->_redirect('*/*/edit', ['_current' => true]);
        }
    }

    public function deleteAction()
    {
        try {
            $model = $this->getCurrentModel();

            $this->deleteModel();
            $this->_getSession()->addSuccess($this->__('The Cookie Details has been deleted.'));

            $this->_redirect('*/cookieVendors/edit', ['id' => $model->getVendorId()]);
        } catch (Exception $e) {
            $this->_getSession()->addError($e->getMessage());
            Mage::logException($e);
            $this->_redirect('*/*/edit', ['_current' => true]);
        }
    }
}
