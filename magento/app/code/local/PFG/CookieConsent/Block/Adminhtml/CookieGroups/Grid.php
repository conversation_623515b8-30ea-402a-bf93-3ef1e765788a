<?php

class PFG_CookieConsent_Block_Adminhtml_CookieGroups_Grid extends Mage_Adminhtml_Block_Widget_Grid
{
    public function __construct()
    {
        parent::__construct();
        $this->setId('grid_id');
        $this->setDefaultSort('id');
        $this->setDefaultDir('asc');
        $this->setSaveParametersInSession(true);
    }

    protected function _prepareCollection(): self
    {
        $collection = Mage::getModel('pfg_cookieconsent/cookieGroups')->getCollection();
        $this->setCollection($collection);

        return parent::_prepareCollection();
    }

    protected function _prepareColumns(): self
    {
        $this->addColumn('id',
            [
                'header' => $this->__('id'),
                'width' => '10px',
                'index' => 'id',
            ]
        );

        $this->addColumn('title',
            [
                'header' => $this->__('Title'),
                'index' => 'title',
            ]
        );

        $this->addColumn('text',
            [
                'header' => $this->__('Text'),
                'index' => 'text',
            ]
        );

        $this->addColumn('vendors',
            [
                'header' => $this->__('Vendors'),
                'index' => 'vendors',
            ]
        );

        return parent::_prepareColumns();
    }

    public function getRowUrl($item): string
    {
        return $this->getUrl('*/*/edit', ['id' => $item->getId()]);
    }

    protected function _prepareMassaction()
    {
        $modelPk = Mage::getModel('pfg_cookieconsent/cookieGroups')->getResource()->getIdFieldName();
        $this->setMassactionIdField($modelPk);
        $this->getMassactionBlock()->setFormFieldName('ids');
        $this->getMassactionBlock()->addItem('delete', [
            'label' => $this->__('Delete'),
            'url' => $this->getUrl('*/*/massDelete'),
        ]);

        return $this;
    }
}
