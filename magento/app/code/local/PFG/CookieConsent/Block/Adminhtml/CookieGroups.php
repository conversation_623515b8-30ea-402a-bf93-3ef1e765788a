<?php

class PFG_CookieConsent_Block_Adminhtml_CookieGroups extends Mage_Adminhtml_Block_Widget_Grid_Container
{
    public function __construct()
    {
        $this->_blockGroup = 'pfg_cookieconsent';
        $this->_controller = 'adminhtml_cookieGroups';
        $this->_headerText = $this->__('Cookie Groups');

        parent::__construct();
    }

    public function getCreateUrl(): string
    {
        return $this->getUrl('*/*/new');
    }
}
