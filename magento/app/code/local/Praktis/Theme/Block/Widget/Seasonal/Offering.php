<?php

class Praktis_Theme_Block_Widget_Seasonal_Offering
    extends Mage_Core_Block_Template
    implements Mage_Widget_Block_Interface
{
    protected $_template = 'praktis/theme/widget/seasonal/offerings.phtml';

    /**
     * @var Praktis_Theme_Model_Resource_Offering_Block_Collection
     */
    protected $_blocksCollection;

    /**
     * @var Praktis_Theme_Model_Offering
     */
    protected $_offering;

    public function getOffering()
    {
        if ($this->_offering === null) {
            $offeringId = $this->getData('offering');
            $this->_offering = Mage::getModel('praktis_theme/offering')->load($offeringId);
        }

        return $this->_offering;
    }

    /**
     * @return Praktis_Theme_Model_Resource_Offering_Block_Collection
     */
    public function getOfferingBlocksCollection()
    {
        if ($this->_blocksCollection === null) {
            $offering = $this->getOffering();
            $this->_blocksCollection = $offering->getBlocksCollection();
        }

        return $this->_blocksCollection;
    }

    /**
     * @param $blockId
     * @return Praktis_Theme_Model_Offering_Block|null
     */
    public function getBlock($blockId)
    {
        $collection = $this->getOfferingBlocksCollection();

        return $collection->getItemById($blockId);
    }

    public function _toHtml()
    {
        $offering = $this->getOffering();
        if (!$offering || $offering->getId() < 1) {
            return $this->__('Invalid Offering');
        }

        return parent::_toHtml();
    }

	/**
	 * @param $categories
	 * @return array<string, string>
	 */
	public function getCategoriesLinks($categories): array
	{
		/** @var Mage_Catalog_Model_Category[] $categoriesCollection */
		$categoriesCollection = Mage::getModel('catalog/category')->getCollection()
			->addIdFilter($categories)
			->addAttributeToSelect('name')
			->addUrlRewriteToResult();

		$res = [];
		foreach ($categoriesCollection as $item) {
			$res[array_search($item->getId(), $categories)] =  array( $item->getName() => $item->getUrl());
		}
		/* Sort by position */
		ksort($res);

		return array_slice($res, 0, 10);
    }
}