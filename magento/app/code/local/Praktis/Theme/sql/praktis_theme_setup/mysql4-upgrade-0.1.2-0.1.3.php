<?php
/** @var $installer Mage_Catalog_Model_Resource_Setup */
$installer = Mage::getResourceModel('catalog/setup', 'catalog_setup');
$installer->startSetup();

$attributeCode = 'category_icon_hover';
if ($installer->getAttribute(Mage_Catalog_Model_Category::ENTITY, $attributeCode)) {
    $installer->removeAttribute(Mage_Catalog_Model_Category::ENTITY, $attributeCode);
}

$installer->addAttribute(Mage_Catalog_Model_Category::ENTITY, $attributeCode, [
    'group' => 'General Information',
    'input' => 'image',
    'type' => 'varchar',
    'label' => 'Icon Hover',
    'backend' => 'catalog/category_attribute_backend_image',
    'global' => Mage_Catalog_Model_Resource_Eav_Attribute::SCOPE_WEBSITE,
    'sort_order' => 6,
    'user_defined' => true,
    'is_user_defined' => true,
    'used_in_product_listing' => true,
    'required' => false,
]);

$attributeId = Mage::getModel('eav/entity_attribute')->getIdByCode(Mage_Catalog_Model_Category::ENTITY, 'thumbnail');
$attribute = Mage::getModel('catalog/resource_eav_attribute')->load($attributeId);
$attribute->setFrontendLabel('Icon')->save();

$installer->endSetup();