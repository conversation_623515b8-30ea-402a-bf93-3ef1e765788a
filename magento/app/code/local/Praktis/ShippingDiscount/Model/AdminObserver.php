<?php
class Praktis_ShippingDiscount_Model_AdminObserver
{
    /**
     * @observes catalog_product_save_before in ADMIN area
     * @param Varien_Event_Observer $observer
     */
    public function productSaveBefore(Varien_Event_Observer $observer)
    {
        if ($actionInstance = Mage::app()->getFrontController()->getAction()) {
            /** @var Mage_Adminhtml_Controller_Action $actionInstance */
            $action = $actionInstance->getFullActionName();
            if ($action != 'adminhtml_catalog_product_save') {
                return;
            }
        } else {
            return;
        }
        /** @var Mage_Catalog_Model_Product $product */
        $product = $observer->getProduct();
        if (!$product || !$product->getId()) {
            return;
        }

        if ($product->hasData(Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_ATTRIBUTE) &&
            $product->getData(Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_ATTRIBUTE) != 0) {
            $startDate = $product->getData(Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_START_DATE);
            $endDate = $product->getData(Praktis_ShippingDiscount_Helper_Data::SHIPPING_DISCOUNT_END_DATE);

            if ($startDate == '' || $endDate == '') {
                Mage::throwException('Free Shipping start date and Free Shipping end date are required when enabling free shipping');
            }

            $startDate = new Zend_Date($startDate, 'yyyy-MM-dd');
            $endDate = new Zend_Date($endDate, 'yyyy-MM-dd');

            if ($startDate->compare($endDate) > 0) {
                Mage::throwException('Free Shipping start date should be before Free Shipping end date');
            }
        }
    }
}