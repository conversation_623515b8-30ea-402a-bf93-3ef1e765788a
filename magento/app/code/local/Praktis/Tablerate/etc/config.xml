<?xml version="1.0"?>
<config>
    <modules>
        <Praktis_Tablerate>
            <version>0.1.1</version>
        </Praktis_Tablerate>
    </modules>

    <global>
        <models>
            <tablerate>
                <class>Praktis_Tablerate_Model</class>
                <resourceModel>tablerate_resource</resourceModel>
            </tablerate>

            <tablerate_resource>
                <class>Praktis_Tablerate_Model_Resource</class>
                <entities>
                    <tablerate>
                        <table>praktis_shipping_tablerate</table>
                    </tablerate>
                </entities>
            </tablerate_resource>
        </models>
        <helpers>
            <tablerate>
                <class>Praktis_Tablerate_Helper</class>
            </tablerate>
        </helpers>
        <blocks>
            <adminhtml>
                <rewrite>
                    <shipping_carrier_tablerate_grid>Praktis_Tablerate_Block_Tablerate_Grid</shipping_carrier_tablerate_grid>
                </rewrite>
            </adminhtml>
        </blocks>

        <resources>
            <praktis_tablerate_setup>
                <setup>
                    <module>Praktis_Tablerate</module>
                    <class>Mage_Sales_Model_Mysql4_Setup</class>
                </setup>
            </praktis_tablerate_setup>
        </resources>
        <events>
            <checkout_type_onepage_save_order_after>
                <observers>
                    <tablerate>
                        <class>tablerate/observer</class>
                        <method>saveOnepageOrder</method>
                    </tablerate>
                </observers>
            </checkout_type_onepage_save_order_after>
            <sales_quote_payment_import_data_before>
                <observers>
                    <tablerate>
                        <class>tablerate/observer</class>
                        <method>setPostcodeAndCityBeforePaymentImport</method>
                    </tablerate>
                </observers>
            </sales_quote_payment_import_data_before>
        </events>
    </global>
    <default>
        <carriers>
            <praktis_tablerate>
                <active>1</active>
                <model>tablerate/tablerate</model>
                <title>Praktis Tablerate Carrier</title>
                <shipping_from>DOOR</shipping_from>
                <cd>1</cd>
                <side>RECEIVER</side>
                <payment_method>CASH</payment_method>
                <currency>BGN</currency>
                <sallowspecific>1</sallowspecific>
                <specificcountry>BG</specificcountry>
            </praktis_tablerate>
        </carriers>
    </default>
</config>
