<?php

class Praktis_Checkout_Shipping_Fields_OfficesController extends Praktis_Checkout_Controller_AbstractController
{
    public function availableAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->norouteAction();
        }

        $data = $this->getRequest()->getPost();

        $shippingCarrier = isset($data['shipping_carrier']) ? (int)trim($data['shipping_carrier']) : 0;
        $city = isset($data['city_id']) ? trim($data['city_id']) : null;

        $response = array();
        try {
            if ($shippingCarrier == Praktis_Checkout_Helper_Shipping::METHOD_ECONT) {
                $response = $this->_getEcontOffices($city);
            }
        } catch (\Exception $e) {
            Mage::logException($e);
            Mage::log($e->getMessage(), null, "custom_praktis_shipping_error.log", true);
        }

        if (empty($response)) {
            return $this->_returnError('Invalid City or Zip/Post Code');
        }

        return $this->_returnResponse($response);
    }

    /**
     * @param $city
     * @param $postCode
     * @param $countryId
     * @return array
     * @throws Varien_Exception
     */
    protected function _getEcontOffices($city)
    {
        if ($city) {
            $offices = $this->_econtHelper()->getOffice($city);
            $result['items'] = $this->_prepOffices($offices);
        } else {
            throw new Varien_Exception('Invalid City and Zip');
        }

        return $result;
    }

    /**
     * @param $offices
     * @return array
     */
    protected function _prepOffices($offices)
    {
        if (empty($offices)) {
            $offices = array();
        }

        $result[] = ['id' => '', 'label' => $this->__(' -- Select Office -- ')];

        return array_merge($result, $offices);
    }

    /**
     * @return Praktis_Checkout_Helper_Shipping_Econt
     */
    protected function _econtHelper()
    {
        return Mage::helper('praktis_checkout/shipping_econt');
    }
}
