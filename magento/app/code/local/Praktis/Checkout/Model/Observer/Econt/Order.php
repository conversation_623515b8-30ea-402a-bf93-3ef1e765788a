<?php

class Praktis_Checkout_Model_Observer_Econt_Order
{
    /**
     * @param Varien_Event_Observer $observer
     * @return $this
     * @throws Exception
     */
    public function onepageUpdateBillingAddress(Varien_Event_Observer $observer)
    {
        return; // PRAK-702
        if ($this->_getSession()->hasExtensaEcont()) {
            $session = $this->_getSession()->getExtensaEcont();
            /** @var Mage_Sales_Model_Order $order */
            $order = $observer->getEvent()->getOrder();

            if (strpos($order->getShippingMethod(), 'extensa_econt') !== false) {
                $econtData = $this->getEcontOrderData($session['econt_order_id']);
                $econtOrderRow = array();

                if ($session['receiver_address']['shipping_to'] == 'OFFICE'
                    || $session['receiver_address']['shipping_to'] == 'APS') {
                    if (isset($econtData['loadings']['to_office']['row'])) {
                        $econtOrderRow = $econtData['loadings']['to_office']['row'];
                    } else if (isset($econtData['loadings']['row'])) {
                        $econtOrderRow = $econtData['loadings']['row'];
                    } else {
                        $econtOrderRow = array();
                    }
                } elseif ($session['receiver_address']['shipping_to'] == 'DOOR') {
                    if (isset($econtData['loadings']['to_door']['row'])) {
                        $econtOrderRow = $econtData['loadings']['to_door']['row'];
                    } else if (isset($econtData['loadings']['row'])) {
                        $econtOrderRow = $econtData['loadings']['row'];
                    } else {
                        $econtOrderRow = array();
                    }
                }

                $receiverAddress = array();

                if ($session['receiver_address']['shipping_to'] !== 'DOOR') {
                    $office = Mage::getModel('extensa_econt/office')->load($session['office_id']);
                    if ($office->getId()) {
                        $addressText = Mage::helper('extensa_econt')->__('До офис:') . ' ';
                        if (Mage::helper('extensa_econt')->getLanguage() == 'bg_BG') {
                            $addressText .= $office->getOfficeCode() . ', ' . $office->getName() . ', ' . $office->getAddress();
                        } else {
                            $addressText .= $office->getOfficeCode() . ', ' . $office->getNameEn() . ', ' . $office->getAddressEn();
                        }
                        $receiverAddress[] = $addressText;
                    }
                } else {
                    if ($econtOrderRow['receiver']['quarter']) {
                        $receiverAddress[] = $econtOrderRow['receiver']['quarter'];
                    }

                    if ($econtOrderRow['receiver']['street']) {
                        $street = $econtOrderRow['receiver']['street'];

                        if ($econtOrderRow['receiver']['street_num']) {
                            $street .= ' ' . $econtOrderRow['receiver']['street_num'];
                        }

                        $receiverAddress[] = $street;
                    }

                    if ($econtOrderRow['receiver']['street_other']) {
                        $receiverAddress[] = $econtOrderRow['receiver']['street_other'];
                    }
                }

                $address = $order->getBillingAddress();

                if (!isset($econtOrderRow['receiver']['post_code']) || empty($econtOrderRow['receiver']['post_code'])) {
                    $post = $order->getShippingAddress()->getPostcode();
                } else {
                    $post = $econtOrderRow['receiver']['post_code'];
                }

                if (!isset($econtOrderRow['receiver']['city']) || empty($econtOrderRow['receiver']['city'])) {
                    $city = $order->getShippingAddress()->getCity();
                } else {
                    $city = $econtOrderRow['receiver']['city'];
                }

                $address
                    ->setRegionId(null)
                    ->setRegion(null)
                    ->setPostcode($post)
                    ->setCity($city)
                    ->setStreet(implode(', ', $receiverAddress))
                    ->setCountryId('BG');
                if ($econtOrderRow['receiver']['name'] != $econtOrderRow['receiver']['name_person']) {
                    $address->setCompany($econtOrderRow['receiver']['name']);
                }

                $address->save();
            }
        }

        return $this;
    }

    /**
     * @param $id
     * @return array
     */
    public function getEcontOrderData($id)
    {
        $econtOrder = Mage::getModel('extensa_econt/order')->load($id);
        return unserialize($econtOrder->getData('data'));
    }

    /**
     * @return Extensa_Econt_Helper_Data
     */
    public function _getEcontHelper()
    {
        return Mage::helper('extensa_econt');
    }

    /**
     * @return Mage_Checkout_Model_Session
     */
    public function _getSession()
    {
        return Mage::getSingleton('checkout/session');
    }
}