<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Checkout
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <api>
        <resources>
            <cart translate="title" module="checkout">
                <model>checkout/cart_api</model>
                <title>Shopping Cart</title>
                <acl>cart</acl>
                <methods>
                    <create translate="title" module="checkout">
                        <title>Create shopping cart</title>
                        <method>create</method>
                        <acl>cart/create</acl>
                    </create>
                    <order translate="title" module="checkout">
                        <title>Create an order from shopping cart</title>
                        <method>createOrder</method>
                        <acl>cart/order</acl>
                    </order>
                    <info translate="title" module="checkout">
                        <title>Retrieve information about shopping cart</title>
                        <method>info</method>
                        <acl>cart/info</acl>
                    </info>
                    <totals translate="title" module="checkout">
                        <title>Get total prices for shopping cart</title>
                        <method>totals</method>
                        <acl>cart/totals</acl>
                    </totals>
                    <license translate="title" module="checkout">
                        <title>Get terms and conditions</title>
                        <method>licenseAgreement</method>
                        <acl>cart/license</acl>
                    </license>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <quote_create_fault>
                        <code>1003</code>
                        <message>Can not create a quote. </message>
                    </quote_create_fault>
                    <quote_already_exists>
                        <code>1004</code>
                        <message>Can not create a quote because quote with such identifier is already exists</message>
                    </quote_already_exists>
                    <required_agreements_are_not_all>
                        <code>1005</code>
                        <message>You did not set all required agreements</message>
                    </required_agreements_are_not_all>
                    <invalid_checkout_type>
                        <code>1006</code>
                        <message>The checkout type is not valid. Select single checkout type.</message>
                    </invalid_checkout_type>
                    <guest_checkout_is_not_enabled>
                        <code>1007</code>
                        <message>Checkout is not available for guest</message>
                    </guest_checkout_is_not_enabled>
                    <create_order_fault>
                        <code>1008</code>
                        <message>Can not create an order. </message>
                    </create_order_fault>
                </faults>
            </cart>
            <cart_product translate="title" module="checkout">
                <model>checkout/cart_product_api</model>
                <title>Cart Product API</title>
                <acl>cart/product</acl>
                <methods>
                    <add translate="title" module="checkout">
                        <title>Add product to shopping cart</title>
                        <method>add</method>
                        <acl>cart/product/add</acl>
                    </add>
                    <update translate="title" module="checkout">
                        <title>Update product quantities in shopping cart</title>
                        <method>update</method>
                        <acl>cart/product/update</acl>
                    </update>
                    <remove translate="title" module="checkout">
                        <title>Remove product from shopping cart</title>
                        <method>remove</method>
                        <acl>cart/product/remove</acl>
                    </remove>
                    <list translate="title" module="checkout">
                        <title>Get list of products in shopping cart</title>
                        <method>items</method>
                        <acl>cart/product/list</acl>
                    </list>
                    <moveToCustomerQuote>
                        <title>Move product(s) to customer quote</title>
                        <method>moveToCustomerQuote</method>
                        <acl>cart/product/moveToCustomerQuote</acl>
                    </moveToCustomerQuote>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <invalid_product_data>
                        <code>1021</code>
                        <message>Product's data is not valid.</message>
                    </invalid_product_data>
                    <add_product_fault>
                        <code>1022</code>
                        <message>Product(s) could not be added. </message>
                    </add_product_fault>
                    <add_product_quote_save_fault>
                        <code>1023</code>
                        <message>Quote could not be saved during adding product(s) operation.</message>
                    </add_product_quote_save_fault>
                    <update_product_fault>
                        <code>1024</code>
                        <message>Product(s) could not be updated. </message>
                    </update_product_fault>
                    <update_product_quote_save_fault>
                        <code>1025</code>
                        <message>Quote could not be saved during updating product(s) operation.</message>
                    </update_product_quote_save_fault>
                    <remove_product_fault>
                        <code>1026</code>
                        <message>Product(s) could not be removed. </message>
                    </remove_product_fault>
                    <remove_product_quote_save_fault>
                        <code>1027</code>
                        <message>Quote could not be saved during removing product(s) operation.</message>
                    </remove_product_quote_save_fault>
                    <customer_not_set_for_quote>
                        <code>1028</code>
                        <message>Customer is not set for quote.</message>
                    </customer_not_set_for_quote>
                    <customer_quote_not_exist>
                        <code>1029</code>
                        <message>Customer's quote is not existed.</message>
                    </customer_quote_not_exist>
                    <quotes_are_similar>
                        <code>1030</code>
                        <message>Quotes are identical.</message>
                    </quotes_are_similar>
                    <unable_to_move_all_products>
                        <code>1031</code>
                        <message>Product(s) could not be moved. </message>
                    </unable_to_move_all_products>
                    <product_move_quote_save_fault>
                        <code>1032</code>
                        <message>One of quote could not be saved during moving product(s) operation.</message>
                    </product_move_quote_save_fault>
                </faults>
            </cart_product>
            <cart_customer translate="title" module="checkout">
                <model>checkout/cart_customer_api</model>
                <title>Customer Information</title>
                <acl>cart/customer</acl>
                <methods>
                    <set translate="title" module="checkout">
                        <title>Set customer for shopping cart</title>
                        <method>set</method>
                        <acl>cart/customer/set</acl>
                    </set>
                    <addresses translate="title" module="checkout">
                        <title>Set customer's addresses in shopping cart</title>
                        <method>setAddresses</method>
                        <acl>cart/customer/addresses</acl>
                    </addresses>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <customer_not_set>
                        <code>1041</code>
                        <message>Customer is not set. </message>
                    </customer_not_set>
                    <customer_not_exists>
                        <code>1042</code>
                        <message>The customer's identifier is not valid or customer is not existed</message>
                    </customer_not_exists>
                    <customer_not_created>
                        <code>1043</code>
                        <message>Customer could not be created. </message>
                    </customer_not_created>
                    <customer_data_invalid>
                        <code>1044</code>
                        <message>Customer data is not valid. </message>
                    </customer_data_invalid>
                    <customer_mode_is_unknown>
                        <code>1045</code>
                        <message>Customer's mode is unknown</message>
                    </customer_mode_is_unknown>
                    <customer_address_data_empty>
                        <code>1051</code>
                        <message>Customer address data is empty.</message>
                    </customer_address_data_empty>
                    <customer_address_invalid>
                        <code>1052</code>
                        <message>Customer's address data is not valid.</message>
                    </customer_address_invalid>
                    <invalid_address_id>
                        <code>1053</code>
                        <message>The customer's address identifier is not valid</message>
                    </invalid_address_id>
                    <address_is_not_set>
                        <code>1054</code>
                        <message>Customer address is not set.</message>
                    </address_is_not_set>
                    <address_not_belong_customer>
                        <code>1055</code>
                        <message>Customer address identifier do not belong customer, which set in quote</message>
                    </address_not_belong_customer>
                </faults>
            </cart_customer>
            <cart_shipping translate="title" module="checkout">
                <model>checkout/cart_shipping_api</model>
                <title>Shipping information</title>
                <acl>cart/shipping</acl>
                <methods>
                    <method translate="title" module="checkout">
                        <title>Set shipping method</title>
                        <method>setShippingMethod</method>
                        <acl>cart/shipping/method</acl>
                    </method>
                    <list translate="title" module="checkout">
                        <title>Get list of available shipping methods</title>
                        <method>getShippingMethodsList</method>
                        <acl>cart/shipping/list</acl>
                    </list>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <shipping_address_is_not_set>
                        <code>1061</code>
                        <message>Can not make operation because of customer shipping address is not set</message>
                    </shipping_address_is_not_set>
                    <shipping_method_is_not_available>
                        <code>1062</code>
                        <message>Shipping method is not available</message>
                    </shipping_method_is_not_available>
                    <shipping_method_is_not_set>
                        <code>1063</code>
                        <message>Can not set shipping method. </message>
                    </shipping_method_is_not_set>
                    <shipping_methods_list_could_not_be_retrived>
                        <code>1064</code>
                        <message>Can not receive list of shipping methods. </message>
                    </shipping_methods_list_could_not_be_retrived>
                </faults>
            </cart_shipping>
            <cart_payment translate="title" module="checkout">
                <model>checkout/cart_payment_api</model>
                <title>Payment method information</title>
                <acl>cart/payment</acl>
                <methods>
                    <method translate="title" module="checkout">
                        <title>Set payment method</title>
                        <method>setPaymentMethod</method>
                        <acl>cart/payment/method</acl>
                    </method>
                    <list translate="title" module="checkout">
                        <title>Get list of available payment methods</title>
                        <method>getPaymentMethodsList</method>
                        <acl>cart/payment/list</acl>
                    </list>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <payment_method_empty>
                        <code>1071</code>
                        <message>Payment method data is empty.</message>
                    </payment_method_empty>
                    <billing_address_is_not_set>
                        <code>1072</code>
                        <message>Customer's billing address is not set. Required for payment method data.</message>
                    </billing_address_is_not_set>
                    <shipping_address_is_not_set>
                        <code>1073</code>
                        <message>Customer's shipping address is not set. Required for payment method data.</message>
                    </shipping_address_is_not_set>
                    <method_not_allowed>
                        <code>1074</code>
                        <message>Payment method is not allowed</message>
                    </method_not_allowed>
                    <payment_method_is_not_set>
                        <code>1075</code>
                        <message>Payment method is not set. </message>
                    </payment_method_is_not_set>
                </faults>
            </cart_payment>
            <cart_coupon translate="title" module="checkout">
                <model>checkout/cart_coupon_api</model>
                <title>Shopping cart ability to set coupon code</title>
                <acl>cart/coupon</acl>
                <methods>
                    <add translate="title" module="checkout">
                        <title>Add coupon code for shopping cart</title>
                        <method>add</method>
                        <acl>cart/coupon/add</acl>
                    </add>
                    <remove translate="title" module="checkout">
                        <title>Remove coupon code from shopping cart</title>
                        <method>remove</method>
                        <acl>cart/coupon/remove</acl>
                    </remove>
                </methods>
                <faults module="checkout">
                    <store_not_exists>
                        <code>1001</code>
                        <message>Can not make operation because store is not exists</message>
                    </store_not_exists>
                    <quote_not_exists>
                        <code>1002</code>
                        <message>Can not make operation because quote is not exists</message>
                    </quote_not_exists>
                    <quote_is_empty>
                        <code>1081</code>
                        <message>Coupon could not be applied because quote is empty.</message>
                    </quote_is_empty>
                    <cannot_apply_coupon_code>
                        <code>1082</code>
                        <message>Coupon could not be applied.</message>
                    </cannot_apply_coupon_code>
                    <coupon_code_is_not_valid>
                        <code>1083</code>
                        <message>Coupon is not valid.</message>
                    </coupon_code_is_not_valid>
                </faults>
            </cart_coupon>
        </resources>
        <resources_alias>
            <cart_customer_address>cart_customer</cart_customer_address>
        </resources_alias>
        <rest>
            <mapping>
                <cart_product>
                    <post>
                        <method>add</method>
                    </post>
                    <delete>
                        <method>remove</method>
                    </delete>
                </cart_product>
                <cart_shipping>
                    <post>
                        <method>method</method>
                    </post>
                </cart_shipping>
                <cart_payment>
                    <post>
                        <method>method</method>
                    </post>
                </cart_payment>
                <cart_customer>
                    <post>
                        <method>set</method>
                    </post>
                </cart_customer>
                <cart_customer_address>
                    <post>
                        <method>addresses</method>
                    </post>
                </cart_customer_address>
                <cart_payment>
                    <post>
                        <method>method</method>
                    </post>
                </cart_payment>
            </mapping>
        </rest>
        <acl>
            <resources>
                <cart translate="title" module="checkout">
                    <title>Shopping Cart</title>
                    <create translate="title" module="checkout">
                        <title>Create shopping cart</title>
                    </create>
                    <order translate="title" module="checkout">
                        <title>Create an order from shopping cart</title>
                    </order>
                    <info translate="title" module="checkout">
                        <title>Retrieve information about shopping cart</title>
                    </info>
                    <totals translate="title" module="checkout">
                        <title>Get total prices for shopping cart</title>
                    </totals>
                    <license translate="title" module="checkout">
                        <title>Get terms and conditions</title>
                    </license>
                    <product translate="title" module="checkout">
                        <title>Products</title>
                        <add translate="title" module="checkout">
                            <title>Add product(s) to shopping cart</title>
                        </add>
                        <update translate="title" module="checkout">
                            <title>Update product(s) quantities in shopping cart</title>
                        </update>
                        <remove translate="title" module="checkout">
                            <title>Remove product(s) from shopping cart</title>
                        </remove>
                        <list translate="title" module="checkout">
                            <title>Get list of products in shopping cart</title>
                        </list>
                        <moveToCustomerQuote>
                            <title>Move product(s) to customer quote</title>
                        </moveToCustomerQuote>
                    </product>
                    <customer translate="title" module="checkout">
                        <title>Customer's information</title>
                        <set translate="title" module="checkout">
                           <title>Set customer for shopping cart</title>
                        </set>
                        <addresses translate="title" module="checkout">
                            <title>Set customer's addresses in shopping cart</title>
                        </addresses>
                    </customer>
                    <shipping translate="title" module="checkout">
                        <title>Shipping methods in shopping cart</title>
                        <method translate="title" module="checkout">
                            <title>Set shipping method</title>
                        </method>
                        <list translate="title" module="checkout">
                            <title>Get list of available shipping methods</title>
                        </list>
                    </shipping>
                    <payment translate="title" module="checkout">
                        <title>Payment methods in shopping cart</title>
                        <method translate="title" module="checkout">
                            <title>Set payment method</title>
                        </method>
                        <list translate="title" module="checkout">
                            <title>Get list of available payment methods</title>
                        </list>
                    </payment>
                    <coupon>
                        <title>Shopping cart ability to set coupon code</title>
                        <add>
                            <title>Add coupon code for shopping cart</title>
                        </add>
                        <remove>
                             <title>Remove coupon code from shopping cart</title>
                        </remove>
                    </coupon>
                </cart>
            </resources>
        </acl>
        <v2>
            <resources_function_prefix>
                <cart>shoppingCart</cart>
                <cart_product>shoppingCartProduct</cart_product>
                <cart_customer>shoppingCartCustomer</cart_customer>
                <cart_shipping>shoppingCartShipping</cart_shipping>
                <cart_payment>shoppingCartPayment</cart_payment>
                <cart_coupon>shoppingCartCoupon</cart_coupon>
            </resources_function_prefix>
        </v2>
    </api>
</config>
