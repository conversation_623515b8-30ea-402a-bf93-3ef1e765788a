<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Api2
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <acl>
        <resources>
            <admin>
                <children>
                    <system>
                        <children>
                            <api>
                                <children>
                                    <rest_roles translate="title" module="api2">
                                        <title>REST - Roles</title>
                                        <sort_order>30</sort_order>
                                        <children>
                                            <add translate="title">
                                                <title>Add</title>
                                                <sort_order>10</sort_order>
                                            </add>
                                            <edit translate="title">
                                                <title>Edit</title>
                                                <sort_order>20</sort_order>
                                            </edit>
                                            <delete translate="title">
                                                <title>Delete</title>
                                                <sort_order>30</sort_order>
                                            </delete>
                                        </children>
                                    </rest_roles>
                                    <rest_attributes translate="title" module="api2">
                                        <title>REST - Attributes</title>
                                        <sort_order>40</sort_order>
                                        <children>
                                            <edit translate="title">
                                                <title>Edit</title>
                                                <sort_order>10</sort_order>
                                            </edit>
                                        </children>
                                    </rest_attributes>
                                </children>
                            </api>
                        </children>
                    </system>
                </children>
            </admin>
        </resources>
    </acl>
    <menu>
        <system>
            <children>
                <api>
                    <children>
                        <rest_roles translate="title" module="api2">
                            <title>REST - Roles</title>
                            <sort_order>30</sort_order>
                            <action>adminhtml/api2_role</action>
                        </rest_roles>
                        <rest_attributes translate="title" module="api2">
                            <title>REST - Attributes</title>
                            <sort_order>40</sort_order>
                            <action>adminhtml/api2_attribute</action>
                        </rest_attributes>
                    </children>
                </api>
            </children>
        </system>
    </menu>
</config>
