<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_ConfigurableSwatches
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <sections>
        <configswatches translate="label" module="configurableswatches">
            <label>Configurable Swatches</label>
            <tab>catalog</tab>
            <sort_order>45</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>

            <groups>
                <general translate="label" module="configurableswatches">
                    <label>General Settings</label>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enabled translate="label">
                            <label>Enabled</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enabled>
                        <swatch_attributes translate="label">
                            <label>Product Attributes to Show as Swatches in Product Detail</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>configurableswatches/system_config_source_catalog_product_configattribute</source_model>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </swatch_attributes>
                        <product_list_attribute translate="label">
                            <label>Product Attribute to Use for Swatches in Product Listing</label>
                            <frontend_type>select</frontend_type>
                            <source_model>configurableswatches/system_config_source_catalog_product_configattribute_select</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </product_list_attribute>
                        <product_list_price_change translate="label" module="configurableswatches">
                            <label>Dynamic Price Change for Swatches in Product Listing</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </product_list_price_change>
                    </fields>
                </general>
                <product_detail_dimensions translate="label comment" module="configurableswatches">
                    <label>Swatch Dimensions on Product Detail Page</label>
                    <comment><![CDATA[Innermost dimensions, not including border, in pixels]]></comment>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <width translate="label">
                            <label>Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </width>
                        <height translate="label">
                            <label>Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </height>
                    </fields>
                </product_detail_dimensions>
                <product_listing_dimensions translate="label comment" module="configurableswatches">
                    <label>Swatch Dimensions in Product Listing</label>
                    <comment><![CDATA[Innermost dimensions, not including border, in pixels]]></comment>
                    <sort_order>30</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <width translate="label">
                            <label>Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </width>
                        <height translate="label">
                            <label>Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </height>
                    </fields>
                </product_listing_dimensions>
                <layered_nav_dimensions translate="label comment" module="configurableswatches">
                    <label>Swatch Dimensions in Layered Navigation</label>
                    <comment><![CDATA[Innermost dimensions, not including border, in pixels]]></comment>
                    <sort_order>40</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <width translate="label">
                            <label>Width</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </width>
                        <height translate="label">
                            <label>Height</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </height>
                    </fields>
                </layered_nav_dimensions>
            </groups>
        </configswatches>
    </sections>
</config>
