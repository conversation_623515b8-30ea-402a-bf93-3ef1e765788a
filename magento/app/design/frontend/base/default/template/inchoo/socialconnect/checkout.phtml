<?php
/**
 * Incho<PERSON> is not affiliated with or in any way responsible for this code.
 *
 * Commercial support is available directly from the [extension author](http://www.techytalk.info/contact/).
 *
 * @category Marko-M
 * @package SocialConnect
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) <PERSON><PERSON> (http://www.techytalk.info)
 * @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */
?>
<div class="inchoo-socialconnect-checkout">
    <div class="<?php echo $this->_getColSet(); ?>">
        <?php if($this->_googleEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-checkout-google">
            <h3><?php echo $this->__('Google Connect') ?></h3>
            <p><?php echo $this->__('You can continue with checkout using your <strong>Google</strong> account.') ?></p>
            <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_google_button')?>
        </div>
        <?php endif; ?>
        <?php if($this->_facebookEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-checkout-facebook">
            <h3><?php echo $this->__('Facebook Connect') ?></h3>
            <p><?php echo $this->__('You can continue with checkout using your <strong>Facebook</strong> account.') ?></p>
            <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_facebook_button')?>
        </div>
        <?php endif; ?>
        <?php if($this->_twitterEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-checkout-twitter">
            <h3><?php echo $this->__('Twitter Connect') ?></h3>
            <p><?php echo $this->__('You can continue with checkout using your <strong>Twitter</strong> account.') ?></p>
            <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_twitter_button')?>
        </div>
        <?php endif; ?>
        <?php if($this->_linkedinEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-checkout-linkedin">
            <h3><?php echo $this->__('LinkedIn Connect') ?></h3>
            <p><?php echo $this->__('You can continue with checkout using your <strong>Linkedin</strong> account.') ?></p>
            <?php echo $this->getChildHtml('inchoo_socialconnect_checkout_linkedin_button')?>
        </div>
        <?php endif; ?>        
    </div>
</div>
