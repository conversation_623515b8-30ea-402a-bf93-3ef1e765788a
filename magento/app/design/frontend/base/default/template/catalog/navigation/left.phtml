<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * Category left navigation
 *
 * @see Mage_Catalog_Block_Navigation
 */
?>
<?php if (!Mage::registry('current_category')) return ?>
<?php $_categories = $this->getCurrentChildCategories() ?>
<?php $_count = is_array($_categories)?count($_categories):$_categories->count(); ?>
<?php if($_count): ?>
<div class="block block-layered-nav">
    <div class="block-title">
        <strong><span><?php echo $this->__('Browse By') ?></span></strong>
    </div>
    <div class="block-content">
        <dl id="narrow-by-list2">
            <dt><?php echo $this->__('Category') ?></dt>
            <dd>
                <ol>
                <?php foreach ($_categories as $_category): ?>
                    <?php if($_category->getIsActive()): ?>
                    <li>
                        <a href="<?php echo $this->getCategoryUrl($_category) ?>"<?php if ($this->isCategoryActive($_category)): ?> class="current"<?php endif; ?>><?php echo $this->escapeHtml($_category->getName()) ?></a> (<?php echo $_category->getProductCount() ?>)
                    </li>
                    <?php endif; ?>
                <?php endforeach ?>
                </ol>
            </dd>
        </dl>
        <script type="text/javascript">decorateDataList('narrow-by-list2')</script>
    </div>
</div>
<?php endif; ?>
