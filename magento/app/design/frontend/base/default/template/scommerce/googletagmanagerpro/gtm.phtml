<?php $helper = Mage::helper('scommerce_googletagmanagerpro');
if ($helper->isEEEnabled()): 
$customer = Mage::getSingleton('customer/session')->getCustomer();
?>
<script>
//<![CDATA[
<?php if ((strlen($customer->getId())) || ($helper->bContactCompleted()) || ($helper->bRegistered())):?>
window.dataLayer = window.dataLayer || [];
<?php endif;?>
<?php if (strlen($customer->getId())):?>
dataLayer.push({
	'event' : 'userIdSet',
	'user_id': '<?php echo $customer->getId();?>'	
});
<?php endif;?>
<?php if ($helper->bContactCompleted()):?>
dataLayer.push({
  'event' : 'contact-form-submitted'
});
<?php endif;?>
<?php if ($helper->bRegistered()):?>
dataLayer.push({
  'event' : 'registration-form-completed'
});
<?php endif;?>

function manipulationOfCart(product, type, list) {
	if (list == undefined){
		list='Category - '+ product.category
	}
	
    if (type == 'add'){
	    dataLayer.push({
		  'event': 'addToCart',
		  'ecommerce': {
			'currencyCode': '<?php echo Mage::app()->getStore()->getCurrentCurrencyCode();?>',
			'add': {                                // 'add' actionFieldObject measures.
			  'actionField': {'list': list},
			  'products': [{                        //  adding a product to a shopping cart.
				'name': product.name,
				'id': product.id,
				'price': product.price,
				'brand': product.brand,
				'category': product.category,
				'quantity': product.qty,
				'list': list
			   }]
			}
		  }
		});
    }
    else if (type == 'remove'){
	    dataLayer.push({
		  'event': 'removeFromCart',
		  'ecommerce': {
			'currencyCode': '<?php echo Mage::app()->getStore()->getCurrentCurrencyCode();?>',
			'remove': {                             // 'remove' actionFieldObject measures.
			  'actionField': {'list': list},
			  'products': [{                        //  adding a product to a shopping cart.
				'name': product.name,
				'id': product.id,
				'price': product.price,
				'brand': product.brand,
				'category': product.category,
				'quantity': product.qty,
				'list': list
			   }]
			}
		  }
		});
    }
}

jQuery(document).ready(function($){
    <?php $addedProduct = Mage::getModel('core/session')->getProductToBasket();
	if ($addedProduct):?>
	var productToBasket = <?php echo $addedProduct?>;
	var productlist = Mage.Cookies.get("productlist");
	//console.log(productlist);
    if (productToBasket != undefined){
        //console.log(productToBasket);
        manipulationOfCart(productToBasket, 'add', productlist);
        Mage.Cookies.clear("productlist");
    }
	<?php Mage::getModel('core/session')->unsProductToBasket();
	endif;?>

    <?php $removedProduct = Mage::getModel('core/session')->getProductOutBasket();
	if ($removedProduct):?>
	var productOutBasket = <?php echo $removedProduct?>;

    if (productOutBasket != undefined){
        manipulationOfCart(productOutBasket, 'remove', '');
    }
	<?php Mage::getModel('core/session')->unsProductOutBasket();
	endif;?>
});
//]]>
</script>
<?php endif;
// DISABLED - tag is loaded via PFG_CookieConsent
if($helper->isEnabled() && false): ?>
<!-- Scommerce Mage Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'//www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','<?php echo $helper->getAccountId() ?>');</script>
<!-- End Scommerce Mage Google Tag Manager -->
<?php endif; ?>

