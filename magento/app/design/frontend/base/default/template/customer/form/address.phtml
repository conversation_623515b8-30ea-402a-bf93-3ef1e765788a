<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">countryRegions = <?php echo $this->helper('directory')->getRegionJson() ?></script>

<div class="page-title">
    <h1><?php if($data->getAddressId()): ?><?php echo $this->__('Edit Address Entry') ?><?php else: ?><?php echo $this->__('New Address Entry') ?><?php endif ?></h1>
</div>
<?php echo $this->getMessagesBlock()->toHtml() ?>
<form action="<?php echo $action ?>" method="post" id="form-validate">
    <div class="fieldset">
    <input type="hidden" name="address_id" value="<?php echo $data->getAddressId() ?>" />
    <input type="hidden" name="customer_id" id="address_id" value="<?php echo $data->getCustomerId() ?>" />
        <h2 class="legend"><?php echo $this->__('Personal Information') ?></h2>
        <ul class="form-list">
            <li class="fields">
                <?php echo $this->getLayout()->createBlock('customer/widget_name')->setObject($data)->toHtml() ?>
            </li>
            <li>
                <label for="company"><?php echo $this->__('Company') ?></label>
                <div class="input-box">
                    <input type="text" name="company" id="company" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Company')) ?>" value="<?php echo $this->escapeHtml($data->getCompany()) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('company') ?>" />
                </div>
            </li>
        </ul>
    </div>
    <div class="fieldset">
        <h2 class="legend"><?php echo $this->__('Address') ?></h2>
        <ul class="form-list">
        <?php $_streetValidationClass = $this->helper('customer/address')->getAttributeValidationClass('street'); ?>
            <li class="wide">
                <label for="street_1" class="required"><em>*</em><?php echo $this->__('Street Address') ?></label>
                <div class="input-box">
                    <input type="text" name="street[]" value="<?php echo $this->escapeHtml($data->getStreet(1)) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Street Address')) ?>" id="street_1" class="input-text <?php echo $_streetValidationClass ?>" />
                </div>
            </li>
        <?php $_streetValidationClass = trim(str_replace('required-entry', '', $_streetValidationClass)); ?>
        <?php for ($_i = 2, $_n = $this->helper('customer/address')->getStreetLines(); $_i <= $_n; $_i++): ?>
            <li class="wide">
                <div class="input-box">
                    <input type="text" name="street[]" value="<?php echo $this->escapeHtml($data->getStreet($_i)) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Street Address %s', $_i)) ?>" id="street_<?php echo $_i ?>" class="input-text <?php echo $_streetValidationClass ?>" />
                </div>
            </li>
        <?php endfor; ?>
            <li class="fields">
                <div class="field">
                    <label for="city" class="required"><em>*</em><?php echo $this->__('City') ?></label>
                    <div class="input-box">
                        <input type="text" name="city" value="<?php echo $this->escapeHtml($data->getCity()) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('City')) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('city') ?>" id="city" />
                    </div>
                </div>
                <div class="field">
                    <label for="region_id" class="required"><em>*</em><?php echo $this->__('State/Province') ?></label>
                    <div class="input-box">
                        <select id="region_id" name="region_id" title="<?php echo Mage::helper('core')->quoteEscape($this->__('State/Province')) ?>" class="validate-select" style="display:none;">
                            <option value=""><?php echo $this->__('Please select region, state or province') ?></option>
                        </select>
                        <script type="text/javascript">
                        //<![CDATA[
                            $('region_id').setAttribute('defaultValue',  "<?php echo $this->getAddress()->getRegionId() ?>");
                        //]]>
                        </script>
                        <input type="text" id="region" name="region" value="<?php echo $this->escapeHtml($this->getAddress()->getRegion()) ?>"  title="<?php echo Mage::helper('core')->quoteEscape($this->__('State/Province')) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('region') ?>" style="display:none;" />
                    </div>
                </div>
            </li>
            <li class="fields">
                <div class="field">
                    <label for="zip" class="required"><em>*</em><?php echo $this->__('Zip/Postal Code') ?></label>
                    <div class="input-box">
                        <input type="text" name="postcode" value="<?php echo $this->escapeHtml($data->getPostcode()) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Zip/Postal Code')) ?>" id="zip" class="input-text validate-zip-international <?php echo $this->helper('customer/address')->getAttributeValidationClass('postcode') ?>" />
                    </div>
                </div>
                <div class="field">
                    <label for="country" class="required"><em>*</em><?php echo $this->__('Country') ?></label>
                    <div class="input-box">
                        <select name="country_id" id="country" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Country')) ?>" class="validate-select">
                            <?php echo $countries->toHtmlOptions($data->getCountryId()) ?>
                        </select>
                    </div>
                </div>
            </li>
            <li class="fields">
                <div class="field">
                    <label for="telephone" class="required"><em>*</em><?php echo $this->__('Telephone') ?></label>
                    <div class="input-box">
                        <input type="text" name="telephone" value="<?php echo $this->escapeHtml($data->getTelephone()) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Telephone')) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('telephone') ?>" id="telephone" />
                    </div>
                </div>
                <div class="field">
                    <label for="fax"><?php echo $this->__('Fax') ?></label>
                    <div class="input-box">
                        <input type="text" name="fax" value="<?php echo $this->escapeHtml($data->getFax()) ?>" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Fax')) ?>" class="input-text <?php echo $this->helper('customer/address')->getAttributeValidationClass('fax') ?>" id="fax" />
                    </div>
                </div>
            </li>
        <?php foreach ($primaryTypes as $code=>$type): ?>
            <li<?php if (!$address->isPrimary($type['address_type_id'])) echo ' class="control"' ?>>
            <?php if ($address->isPrimary($type['address_type_id'])): ?>
                <strong><?php echo $this->__("This is My Default %s Address", ucfirst($type['name'])) ?></strong>
            <?php else: ?>
                <input type="checkbox" id="primary_<?php echo $code ?>" name="primary_types[]" value="<?php echo $type['address_type_id'] ?>" class="checkbox" /><label for="primary_<?php echo $code ?>"><?php echo $this->__("Use as My Default %s Address", ucfirst($type['name'])) ?></label>
            <?php endif ?>
            </li>
        <?php endforeach ?>
        </ul>
    </div>
    <div class="buttons-set">
        <p class="required"><?php echo $this->__('* Required Fields') ?></p>
        <p class="back-link"><a href="<?php echo $this->getUrl('customer/address/') ?>"><small>&laquo; </small><?php echo $this->__('Back') ?></a></p>
        <button data-action="save-customer-address" type="submit" class="button" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Save Address')) ?>"><span><span><?php echo $this->__('Save Address') ?></span></span></button>
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    var dataForm = new VarienForm('form-validate', true);
    //dataForm.setElementsRelation('country', 'state', '<?php echo $this->getUrl('directory/json/childRegion') ?>');
    new RegionUpdater('country', 'region', 'region_id', countryRegions, undefined, 'zip');
//]]>
</script>
