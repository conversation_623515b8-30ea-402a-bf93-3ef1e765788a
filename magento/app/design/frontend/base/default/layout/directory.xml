<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

-->
<layout version="0.1.0">

<!--
Category default layout
-->
    <default>
        <reference name="head">
            <block type="core/template" name="optional_zip_countries" as="optional_zip_countries" template="directory/js/optional_zip_countries.phtml" />
        </reference>
    </default>

    <catalog_category_default>
        <reference name="left">
            <block type="directory/currency" name="currency" before="catalog.leftnav" template="directory/currency.phtml"/>
        </reference>
    </catalog_category_default>

<!--
Category layered navigation layout
-->

    <catalog_category_layered>
        <reference name="left">
            <block type="directory/currency" name="currency" before="catalog.leftnav" template="directory/currency.phtml"/>
        </reference>
    </catalog_category_layered>

<!--
Catalog Search layout
-->

    <catalogsearch_advanced_index>
        <reference name="left">
            <block type="directory/currency" name="right_currency" before="-" template="directory/currency.phtml"/>
        </reference>
    </catalogsearch_advanced_index>

    <catalogsearch_result_index>
        <reference name="left">
            <block type="directory/currency" name="currency" before="-" template="directory/currency.phtml"/>
        </reference>
    </catalogsearch_result_index>

    <catalogsearch_advanced_result>
        <reference name="right">
            <block type="directory/currency" name="right_currency" before="-" template="directory/currency.phtml"/>
        </reference>
    </catalogsearch_advanced_result>

</layout>
