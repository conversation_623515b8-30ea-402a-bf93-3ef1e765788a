<?xml version="1.0"?>
<layout version="0.1.0">
    <stenik_partner_partner_list>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_partner/partner_listPage" name="partner.list" template="stenik/partner/partner/list.phtml"/>
        </reference>
    </stenik_partner_partner_list>

    <stenik_partner_group_list>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_partner/group_listPage" name="group.list" template="stenik/partner/group/list.phtml"/>
        </reference>
    </stenik_partner_group_list>

    <stenik_partner_partner_view>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>

        <reference name="left">
            <block type="stenik_partner/group_navigation" name="group.navigation" template="stenik/partner/group/navigation.phtml"/>
        </reference>

        <reference name="content">
            <block type="stenik_partner/partner_view" name="partner.view" template="stenik/partner/partner/view.phtml"/>
        </reference>
    </stenik_partner_partner_view>

    <stenik_partner_group_view>
        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>

        <reference name="left">
            <block type="stenik_partner/group_navigation" name="group.navigation" template="stenik/partner/group/navigation.phtml"/>
        </reference>

        <reference name="content">
            <block type="stenik_partner/group_view" name="group.view" template="stenik/partner/group/view.phtml">
                <block type="stenik_partner/partner_list" name="partner.list" template="stenik/partner/partner/list.phtml"/>
            </block>
        </reference>
    </stenik_partner_group_view>

    <stenik_partner_partnership_request>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>

        <reference name="content">
            <block type="stenik_partner/partnership_formPage" name="partnership.form" template="stenik/partner/partnership/form.phtml"/>
        </reference>
    </stenik_partner_partnership_request>
</layout>