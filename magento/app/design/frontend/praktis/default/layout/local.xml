<?xml version="1.0"?>
<layout version="0.1.0">

    <default>
        <reference name="head">
            <action method="addJs">
                <name>tippy/popper.min.js</name>
            </action>
            <action method="addJs">
                <name>tippy/tippy-bundle.umd.min.js</name>
            </action>
        </reference>
        <reference name="header">
            <block type="cms/block" name="headerBanner">
                <action method="setBlockId">
                    <identifier>header_banner</identifier>
                </action>
            </block>
        </reference>

        <reference name="head">
            <block type="praktis_seo/canonical" name="praktis_seo" as="praktis_seo"/>
        </reference>


        <reference name="catalog.topnav">
            <block type="praktis_theme/topmenu_renderer" name="catalog.topnav.renderer" template="praktis/topmenu/renderer.phtml"/>
        </reference>

        <reference name="footer">
            <block type="core/template" name="footer_newsletter" as="footer_newsletter" template="page/html/footer/newsletter.phtml">
                <block type="cms/block" name="newsletter-text">
                    <action method="setBlockId">
                        <identifier>newsletter-text</identifier>
                    </action>
                </block>
                <block type="newsletter/subscribe" name="newsletter" template="newsletter/subscribe.phtml"/>
            </block>
            <block type="cms/block" name="footer-mid">
                <action method="setBlockId">
                    <identifier>footer-mid</identifier>
                </action>
            </block>
            <block type="cms/block" name="footer-bottom">
                <action method="setBlockId">
                    <identifier>footer-bottom</identifier>
                </action>
            </block>
        </reference>

        <reference name="top.links">
            <remove name="flexibleforms_top_link"/>
        </reference>

        <!-- Product labels -->
        <block type="core/template" name="product.labels" template="catalog/product/labels.phtml"/>
        <remove name="wishlist_link"/>


        <remove name="left.newsletter"/>
        <remove name="left.permanent.callout"/>
        <remove name="right.permanent.callout"/>
        <remove name="product.clone_prices"/>
        <remove name="cart_sidebar"/>
        <remove name="sale.reorder.sidebar"/>
        <remove name="catalog.product.related"/>
        <remove name="right.reports.product.viewed"/>
        <remove name="right.reports.product.compared"/>
        <remove name="right.poll"/>
        <remove name="tags_popular"/>
        <remove name="paypal.partner.right.logo"/>
        <remove name="sale.reorder.sidebar"/>
        <remove name="customer_account_dashboard_info1"/>
        <remove name="product_review_list.count"/>
        <remove name="currency"/>
    </default>

    <catalog_product_view>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="product.info">
            <block type="cms/block" name="product-delivery-info">
                <action method="setBlockId">
                    <identifier>product_delivery_info</identifier>
                </action>
            </block>
            <block type="praktis_catalog/product_view_calculator" name="area_calculator" as="area_calculator" />
            <block type="stenik_site/product_shopsStock" name="product.info.stock_in_shops" as="stock_in_shops" template="stenik/site/catalog/product/shopsStock.phtml" >
                <block type="cms/block" name="legend-additional">
                    <action method="setBlockId"><identifier>legend-additional</identifier></action>
                </block>
            </block>
            <block type="core/template" name="bundler" template="praktis/bundler.phtml"/>
            <block type="praktis_catalog/product_view_calculator" name="area_calculator" as="area_calculator"/>
            <block type="stenik_site/product_shopsStock" name="product.info.stock_in_shops" as="stock_in_shops"
                   template="stenik/site/catalog/product/shopsStock.phtml"/>
            <block type="core/template" name="productJetCredit" template="stenik/leasingjetcredit/form_product.phtml"/>
            <block type="core/template" name="productUnicredit"
                   template="pfg/unicredit_leasing/form_product_loader.phtml"/>
            <block type="cms/block" name="credit-calculator-additional" as="credit-calculator-additional">
                <action method="setBlockId">
                    <identifier>credit_calculator_additional</identifier>
                </action>
            </block>
        </reference>
        <reference name="product.description">
            <block type="catalog/product_view_attributes" name="description_attributes" template="catalog/product/view/attributes.phtml"/>
        </reference>
    </catalog_product_view>

    <catalog_category_view>
        <reference name="left">
            <action method="unsetChild"><name>amshopby.navleft</name></action>
        </reference>
        <reference name="root">
            <action method="setTemplate"><template>page/1column-wide.phtml</template></action>
        </reference>
        <!--<reference name="content">-->
            <!--<action method="insert"><block>amshopby.navleft</block></action>-->
        <!--</reference>-->
        <!--<reference name="content">-->
            <!--<block type="amshopby/catalog_layer_view" name="amshopby.navleft" template="catalog/layer/view.phtml"/>-->
        <!--</reference>-->

    </catalog_category_view>

    <checkout_cart_index>
        <update handle="stenik_jquery_owl_carousel" />

        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="checkout">
                <crumbName>cart</crumbName>
                <crumbInfo><label>Your Shopping Cart</label><title>Your Shopping Cart</title></crumbInfo>
            </action>
        </reference>

        <reference name="content">
            <action method="unsetChild"><name>checkout.cart</name></action>
            <block type="page/html_wrapper" name="checkout.cartWrapper" as="cartWrapper">
                <action method="setElementClass"><value>cartWrapper</value></action>
                <action method="insert"><blockName>checkout.cart</blockName></action>
            </block>
<!--            <block type="checkout/cart_crosssell" name="checkout.cart.crosssell" as="crosssell" template="checkout/cart/crosssell.phtml"/>-->
        </reference>
    </checkout_cart_index>

    <checkout_onepage_success translate="label">
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
    </checkout_onepage_success>

    <CATEGORY_CUSTOM_TEMPLATE_landing>
        <update handle="stenik_jquery_owl_carousel" />

        <reference name="root">
            <action method="setTemplate"><template>page/2columns-left.phtml</template></action>
        </reference>

        <reference name="category.products">
            <action method="setTemplate"><template>catalog/category/view/landing.phtml</template></action>
        </reference>
    </CATEGORY_CUSTOM_TEMPLATE_landing>

    <contacts_index_index>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="head">
            <action method="setTitle" translate="title" module="contacts"><title>Contacts</title></action>
        </reference>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="contacts">
                <crumbName>contacts</crumbName>
                <crumbInfo><label>Contacts</label><title>Contacts</title></crumbInfo>
            </action>
        </reference>
        <reference name="contactForm">
            <block type="cms/block" name="contactsInfo">
                <action method="setBlockId"><block_id>contacts_info</block_id></action>
            </block>
        </reference>
    </contacts_index_index>

    <checkout_onepage_index>
        <reference name="head">
            <block type="praktis_checkout/checkout_onepage_hotJar" name="hotjar" as="hotjar"/>
        </reference>
    </checkout_onepage_index>

    <customer_account_login>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>Home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page 111111</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>login</crumbName>
                <crumbInfo><label>Log in</label><title>Log in</title></crumbInfo>
            </action>
        </reference>
        <reference name="customer_form_login">
            <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_google_login_button" />
            <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_facebook_login_button" />
            <block type="cms/block" name="socialNetworksText">
                <action method="setBlockId"><identifier>social_networks_text</identifier></action>
            </block>
            <block type="cms/block" name="login_register_static_block">
                <action method="setBlockId"><identifier>login_register_static_block</identifier></action>
            </block>
        </reference>
    </customer_account_login>

    <customer_account_create>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>register</crumbName>
                <crumbInfo><label>Register</label><title>Register</title></crumbInfo>
            </action>
        </reference>
        <reference name="customer_form_register">
            <action method="setTemplate"><template>persistent/customer/form/register.phtml</template></action>
            <block type="inchoo_socialconnect/google_button" name="inchoo_socialconnect_checkout_google_button" />
            <block type="inchoo_socialconnect/facebook_button" name="inchoo_socialconnect_checkout_facebook_button" />
            <block type="cms/block" name="socialNetworksText">
                <action method="setBlockId"><identifier>social_networks_text</identifier></action>
            </block>

        </reference>
    </customer_account_create>

    <customer_account_logoutsuccess>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>logout</crumbName>
                <crumbInfo><label>Logout</label><title>Logout</title></crumbInfo>
            </action>
        </reference>
    </customer_account_logoutsuccess>

    <customer_account_forgotpassword>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>forgotpassword</crumbName>
                <crumbInfo><label>Forgot Password</label><title>Forgot Password</title></crumbInfo>
            </action>
        </reference>
    </customer_account_forgotpassword>

    <customer_account_resetpassword>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>resetPassword</crumbName>
                <crumbInfo><label>Reset a Password</label><title>Reset a Password</title></crumbInfo>
            </action>
        </reference>
    </customer_account_resetpassword>

    <customer_account_changeforgotten>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>changePassword</crumbName>
                <crumbInfo><label>Reset a Password</label><title>Reset a Password</title></crumbInfo>
            </action>
        </reference>
    </customer_account_changeforgotten>

    <customer_account>
        <reference name="root">
            <action method="addBodyClass"><class>customer-account</class></action>
        </reference>
        <reference name="left">
            <action method="unsetChild"><block>cart_sidebar</block></action>
            <remove name="catalog.compare.sidebar"/>
            <remove name="left.permanent.callout"/>
            <remove name="sale.reorder.sidebar"/>
        </reference>

        <reference name="customer_account_navigation">
            <action method="addLink" translate="label" module="customer"><name>billing_agreements</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>recurring_profiles</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>reviews</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>tags</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>downloadable_products</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>OAuth Customer Tokens</name><path></path><label></label></action>
            <action method="addLink" translate="label" module="customer"><name>logoutLink</name><path>customer/account/logout/</path><label>Logout</label></action>
        </reference>
    </customer_account>

    <customer_account_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>dashboard</crumbName>
                <crumbInfo><label>My Dashboard</label><title>My Dashboard</title></crumbInfo>
            </action>
        </reference>
    </customer_account_index>

    <customer_account_edit>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>dashboard</crumbName>
                <crumbInfo><label>Account Information</label><title>Account Information</title>
                </crumbInfo>
            </action>
        </reference>
    </customer_account_edit>

    <customer_address_index>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>address book</crumbName>
                <crumbInfo><label>Address Book</label><title>Address Book</title></crumbInfo>
            </action>
        </reference>
    </customer_address_index>

    <customer_address_form>
        <reference name="breadcrumbs">
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title">
                <crumbName>home</crumbName>
                <crumbInfo><label>Home</label><title>Go to Home Page</title><link>/</link></crumbInfo>
            </action>
            <action method="addCrumb" translate="crumbInfo.label crumbInfo.title" module="customer">
                <crumbName>address book</crumbName>
                <crumbInfo><label>Address Book</label><title>Address Book</title></crumbInfo>
            </action>
        </reference>
    </customer_address_form>

    <cms_page>
        <reference name="left">
            <block name="cms_left" type="praktis_theme/frontend_cms_leftMenu" />
        </reference>
    </cms_page>

    <cms_index_index>
        <reference name="content">
            <block type="core/template" name="google_sitelinks_search" before="-" template="searchindex/google_sitelinks_search.phtml" />
        </reference>
    </cms_index_index>

    <stenik_zeron_order_package_page>
        <reference name="head">
            <action method="addCss"><stylesheet>css/praktis.css</stylesheet></action>
            <action method="addItem"><type>skin_js</type><name>js/praktis.js</name></action>
            <action method="addItem"><type>skin_js</type><name>js/praktis.js.map</name></action>
        </reference>
    </stenik_zeron_order_package_page>
</layout>
