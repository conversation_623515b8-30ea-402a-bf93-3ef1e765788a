<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2018 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Category view template
 *
 * @see Mage_Catalog_Block_Category_View
 */
?>
<?php
$_helper = $this->helper('catalog/output');
$_category = $this->getCurrentCategory();
$_imgHtml = '';
$_headerImageUrl = $_category->getHeaderImageUrl();
if ($_imgUrl = $_category->getImageUrl()) {
    if($_headerImageUrl) {
        $_imgHtml = '<div class="category-image text-center my-3 w-100"><a href="'.$_headerImageUrl.'"><img class="img-fluid w-100" src="' . $_imgUrl . '" alt="' . $this->escapeHtml($_category->getName()) . '" title="' . $this->escapeHtml($_category->getName()) . '" /></a></div>';
    } else {
        $_imgHtml = '<div class="category-image text-center my-3 w-100"><img class="img-fluid w-100" src="' . $_imgUrl . '" alt="' . $this->escapeHtml($_category->getName()) . '" title="' . $this->escapeHtml($_category->getName()) . '" /></div>';
    }
    $_imgHtml = $_helper->categoryAttribute($_category, $_imgHtml, 'image');
}

/* Catalog images module */
if($_catalogImage = Mage::helper('pfg_catalogimages')->getCatalogImage($_category->getId())) {
    if($_catalogImage['mobile_image']) {
		$_imgHtml = sprintf(
			"<a href='%s' class='d-flex w-100'>
                        <img class='img-fluid w-100 d-block d-md-none' src='%s' alt='%s' title='%s' />
                        <img class='img-fluid w-100 d-none d-md-block' src='%s' alt='%s' title='%s' />
                    </a>",
			$this->escapeUrl($_catalogImage['link']),
			$_catalogImage['mobile_image'],
			$this->escapeHtml($_category->getName()),
			$this->escapeHtml($_category->getName()),
			$_catalogImage['image'],
			$this->escapeHtml($_category->getName()),
			$this->escapeHtml($_category->getName())
		);
    } else {
		$_imgHtml = sprintf(
			"<a href='%s' class='d-flex w-100'><img class='img-fluid w-100' src='%s' alt='%s' title='%s' /></a>",
			$this->escapeUrl($_catalogImage['link']),
			$_catalogImage['image'],
			$this->escapeHtml($_category->getName()),
			$this->escapeHtml($_category->getName())
		);
    }
}

$_parents   = $_category->getParentCategories();
?>

<?php if ($_imgHtml): ?>
    <div class="row">
		<?php echo $_imgHtml ?>
    </div>
<?php endif; ?>

<div class="page-title category-title my-3">
    <h1 class="d-block d-sm-none"><?php echo $_helper->categoryAttribute($_category, $_category->getName(), 'name') ?></h1>
</div>


<?php echo $this->getMessagesBlock()->toHtml() ?>

