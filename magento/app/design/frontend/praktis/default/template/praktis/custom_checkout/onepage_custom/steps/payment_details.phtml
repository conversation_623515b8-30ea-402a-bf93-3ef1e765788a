<?php
/** @var Praktis_CustomCheckout_Block_Checkout_Onepage_Steps_PaymentDetails $this */
/** @var Praktis_Checkout_Helper_OrderConfirm $orderConfirmHelper */
$orderConfirmHelper = Mage::helper('praktis_checkout/orderConfirm');
?>
<div class="col-12">
    <div class="card bg-white rounded-0 shadow-sm">
        <div class="card-body">
            <div class="row">
                <?php echo $this->getPaymentMethodsHtml(); ?>
            </div>
            <div class="row">
                <div class="col">
                    <div class="form-group input-group">
                        <input type="text" class="form-control custom-form rounded-0" id="custom-checkout-promocode" name="coupon_code" aria-describedby="custom-checkout-promocode-submit"
                               value="<?php echo $this->getInputStepData('coupon_code') ?>" placeholder="<?php echo $this->__('Enter your promo code here') ?>">
                        <div class="input-group-append">
                            <button id="custom-checkout-promocode-submit" class="btn btn-primary rounded-0"><?php echo $this->__('Apply') ?></button>
                            <button id="custom-checkout-promocode-cancel" style="display: none;" class="btn btn-danger rounded-0"><?php echo $this->__('Remove') ?></button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="ordecomment-comment"><?php echo $this->__('Order comment') ?></label>
                        <textarea class="form-control custom-form rounded-0" id="ordecomment-comment" name="ordercomment[comment]" rows="3"></textarea>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col">
                    <?php echo Mage::helper('praktis_customcheckout/renderer')->getAgreementsHtml(); ?>
                </div>
                <div class="invalid-feedback">
                    <?php echo $this->__('Please select the agreements'); ?>
                </div>
            </div>
            <?php if($orderConfirmHelper->canCurrentCustomerChoseOrderConfirmation()): ?>
                <div class="row">
                    <div class="col" style='margin-top: 5px'>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" name="phone_verification" class="custom-control-input"
                                   id="checkout-invoice_company_has_vat"
                                   value="1" <?= $orderConfirmHelper->getDefaultValue() ? 'checked="checked"' : '' ?>>
                            <label class="custom-control-label custom-control-label-large"
                                   for="checkout-invoice_company_has_vat"><?= $orderConfirmHelper->getConfirmMessage() ?></label>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script type="text/javascript">
    var stepBindings = function () {
        jQuery("#custom_checkout_payment_method").change(function (event) {
            const additionalDataId = 'dd_method_' + jQuery(this).val();
            const additionalDataElement = jQuery('#' + additionalDataId);
            if (additionalDataElement.length > 0) {
                jQuery(".payment-method-additional").hide();
                additionalDataElement.show();
                additionalDataElement.children().each(function (_, e) {
                  if (e.nodeName && e.nodeName.toLowerCase() === 'style') {
                    return;
                  }

                  jQuery(e).show()
                })
            }

            if (additionalDataId === 'dd_method_unicredit_leasing') {
              disableCompleteOrderButton(Translator.translate('Please select a leasing period'))
            } else {
              enableCompleteOrderButton();
            }

            /* Show loader */
            jQuery('body').addClass('overlay');
            jQuery("#praktis-onepage-form input[type=text]").attr('readonly', 'readonly');
            jQuery.ajax({
                method: "POST",
                url: '<?php echo $this->getUpdatePaymentUrl() ?>',
                data: jQuery("#praktis-onepage-form").serialize(),
                success: function (successResult) {
                    if (successResult.hasOwnProperty('redirect')) {
                        window.location.href = successResult.redirect;
                        return;
                    }

                    /* Hide loader */
                    jQuery('body').removeClass('overlay');
                    jQuery("#praktis-onepage-form input[type=text]").removeAttr('readonly');
                    jQuery("[id^='checkout-']").removeClass('is-invalid');

                    if (successResult.hasOwnProperty('review')) {
                        jQuery("#custom_checkout_review").html(successResult.review);
                        jQuery(successResult.review).find("script").each(function () {
                            eval(jQuery(this).text());
                            stepBindings();
                        });
                    }
                },
                error: function (errorResult) {
                    if (errorResult.hasOwnProperty('responseJSON')) {
                        if (errorResult.responseJSON.hasOwnProperty('redirect')) {
                            window.location.href = errorResult.responseJSON.redirect;
                            return;
                        }

                        /* Hide loader */
                        jQuery('body').removeClass('overlay');
                        jQuery("#praktis-onepage-form input[type=text]").removeAttr('readonly');
                        jQuery("[id^='checkout-']").removeClass('is-invalid');

                        if (
                            errorResult.responseJSON.hasOwnProperty('error') && errorResult.responseJSON.error
                            && errorResult.responseJSON.hasOwnProperty('messages') && !jQuery.isEmptyObject(errorResult.responseJSON.messages)
                        ) {
                            jQuery.each(errorResult.responseJSON.messages, function (fieldName, errorMessage) {
                                if (fieldName === 'billing') {
                                    jQuery.each(errorMessage, function (id, message) {
                                        jQuery('#checkout-' + id).addClass('is-invalid');
                                    })
                                } else if (fieldName === 0) {
                                    jQuery('#agreement-1').addClass('is-invalid');
                                } else {
                                    jQuery('#checkout-' + fieldName).addClass('is-invalid');
                                }
                            });
                            // Scroll error into view
                            if (jQuery("[id^='checkout-']").hasClass('is-invalid')) {
                                jQuery('html, body').animate({
                                    scrollTop: jQuery("[id^='checkout-'].is-invalid").offset().top - 50
                                }, 2000);
                            } else if (jQuery('#agreement-1').hasClass('is-invalid')) {
                                jQuery('html, body').animate({
                                    scrollTop: jQuery('#agreement-1').offset().top - 50
                                }, 2000);
                            }
                        }
                    }
                }
            });
        });

        jQuery("#custom-checkout-promocode-submit").click(function(event){
            /* Show loader */
            jQuery('body').addClass('overlay');
            jQuery("#praktis-onepage-form input[type=text]").attr('readonly', 'readonly');
            event.preventDefault();
            jQuery.ajax({
                method: "POST",
                url: '<?php echo $this->getApplyCouponCodeUrl() ?>',
                data: jQuery("#praktis-onepage-form").serialize(),
                dataType: 'json',
                success: function (successResult) {
                    if (successResult.hasOwnProperty('redirect')) {
                        window.location.href = successResult.redirect;
                        return;
                    }

                    /* Hide loader */
                    jQuery('body').removeClass('overlay');
                    jQuery("#praktis-onepage-form input[type=text]").removeAttr('readonly');
                    jQuery("[id^='checkout-']").removeClass('is-invalid');

                    if (successResult.hasOwnProperty('review')) {
                        jQuery("#custom_checkout_review").html(successResult.review);
                        jQuery(successResult.review).find("script").each(function () {
                            eval(jQuery(this).text());
                            stepBindings();
                        });
                    }

                    if (successResult.hasOwnProperty('step_html')) {
                        jQuery("#custom_checkout_steps_holder").html(successResult.step_html);
                        jQuery(successResult.step).find("script").each(function () {
                            eval(jQuery(this).text());
                        });
                        stepBindings();
                    }

                    if (successResult.hasOwnProperty('has_coupon')) {
                        if (successResult.has_coupon) {
                            jQuery('#custom-checkout-promocode-submit').hide();
                            jQuery('#custom-checkout-promocode-cancel').show();
                        }
                    }

                    var applyMessage = false;
                    if (successResult.hasOwnProperty('apply_message')) {
                        applyMessage = successResult.apply_message;
                    }

                    if (successResult.hasOwnProperty('applied_coupon')) {
                        if (successResult.applied_coupon) {
                            jQuery("#custom-checkout-promocode").before('<span class="coupon-message-success">' + applyMessage + '<span>');
                        } else {
                            jQuery("#custom-checkout-promocode").before('<span class="coupon-message-error">' + applyMessage + '<span>');
                        }
                    }
                },
                error: function (errorResult) {
                    if (errorResult.hasOwnProperty('responseJSON')) {
                        if (errorResult.responseJSON.hasOwnProperty('redirect')) {
                            window.location.href = errorResult.responseJSON.redirect;
                            return;
                        }

                        /* Hide loader */
                        jQuery('body').removeClass('overlay');
                        jQuery("#praktis-onepage-form input[type=text]").removeAttr('readonly');
                        jQuery("[id^='checkout-']").removeClass('is-invalid');

                        if (
                            errorResult.responseJSON.hasOwnProperty('error') && errorResult.responseJSON.error
                            && errorResult.responseJSON.hasOwnProperty('messages') && !jQuery.isEmptyObject(errorResult.responseJSON.messages)
                        ) {
                            jQuery.each(errorResult.responseJSON.messages, function (fieldName, errorMessage) {
                                if(fieldName === 'billing') {
                                    jQuery.each(errorMessage, function (id, message) {
                                        jQuery('#checkout-'+ id).addClass('is- invalid');
                                    })
                                } else if(fieldName === 0) {
                                    jQuery('#agreement-1').addClass('is-invalid');
                                } else {
                                    jQuery('#checkout-'+ fieldName).addClass('is-invalid');
                                }
                            });
                            // Scroll error into view
                            if(jQuery("[id^='checkout-']").hasClass('is-invalid')) {
                                jQuery('html, body').animate({
                                    scrollTop: jQuery("[id^='checkout-'].is-invalid").offset().top - 50
                                }, 2000);
                            } else if (jQuery('#agreement-1').hasClass('is-invalid')) {
                                jQuery('html, body').animate({
                                    scrollTop: jQuery('#agreement-1').offset().top - 50
                                }, 2000);
                            }
                        }
                    }
                }
            });

            return false;
        });
    };
</script>
