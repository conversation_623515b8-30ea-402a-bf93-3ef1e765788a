<?php
/**
* Inchoo
*
* NOTICE OF LICENSE
*
* This source file is subject to the Open Software License (OSL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/osl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Please do not edit or add to this file if you wish to upgrade
* Magento or this extension to newer versions in the future.
** Inchoo *give their best to conform to
* "non-obtrusive, best Magento practices" style of coding.
* However,* Inchoo *guarantee functional accuracy of
* specific extension behavior. Additionally we take no responsibility
* for any possible issue(s) resulting from extension usage.
* We reserve the full right not to provide any kind of support for our free extensions.
* Thank you for your understanding.
*
* @category Inchoo
* @package SocialConnect
* <AUTHOR> <marko.mart<PERSON><PERSON>@inchoo.net>
* @copyright Copyright (c) Inchoo (http://inchoo.net/)
* @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
*/
?>
<div class="inchoo-socialconnect-account">
    <div class="page-title">
        <h1><?php echo $this->__('Facebook Connect') ?></h1>
    </div>

    <?php if($this->_hasData()): ?>

    <?php
        $googleId = $this->_getFacebookId();
        $status = $this->_getStatus();
        $email = $this->_getEmail();
        $name = $this->_getName();
        $picture = $this->_getPicture();
        $gender = $this->_getGender();
        $birthday = $this->_getBirthday();
    ?>

    <div class="col3-set">
        <div class="box">
            <div class="box-content">
                <div class="row">
                    <div class="col-12 col-sm-4">
                        <?php if(!empty($picture)): ?>
                            <p>
                                <img src="<?php echo $picture; ?>" alt="<?php echo $this->htmlEscape($name); ?>" />
                            </p>
                        <?php endif; ?>
                    </div>
                    <div class="col-12 col-sm-4">
                        <?php if(!empty($status)): ?>
                            <p>
                                <?php printf($this->__('Connected as %s', '<strong>'.$status.'</strong>')) ?>
                            </p>
                        <?php endif; ?>
                        <?php if(!empty($email)): ?>
                            <p>
                                <?php printf($this->__('Email: %s', '<strong>'.$email.'</strong>')) ?>
                            </p>
                        <?php endif; ?>
                        <?php if(!empty($googleId)): ?>
                            <p>
                                <?php printf($this->__('Facebook #: %s', '<strong>'.$googleId.'</strong>')) ?>
                            </p>
                        <?php endif; ?>
                        <?php if(!empty($gender)): ?>
                            <p>
                                <?php printf($this->__('Gender: %s', '<strong>'.$gender.'</strong>')) ?>
                            </p>
                        <?php endif; ?>
                        <?php if(!empty($birthday)): ?>
                            <p>
                                <?php printf($this->__('Birthday: %s', '<strong>'.$birthday.'</strong>')) ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    <div class="col-12 col-sm-4">
                        <?php echo $this->getChildHtml('inchoo_socialconnect_account_facebook_button'); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php else: ?>
    <div class="col2-set">
        <div class="box-content">
            <div class="row">
                <div class="col-12 col-sm-6">
                    <p>
                        <?php echo $this->__('You can connect store account with your Facebook account so you could login easier in the future.') ?>
                    </p>
                </div>
                <div class="col-12 col-sm-6">
                    <?php echo $this->getChildHtml('inchoo_socialconnect_account_facebook_button'); ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
