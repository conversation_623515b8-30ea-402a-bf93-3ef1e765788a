<div class="full-width" style="z-index: 553;">
    <div class="parallax-container">
        <div class="parallax-title-wrapper">
            <?php if ($this->getTitle()): ?>
                <a href="<?php echo $this->getData('url'); ?>" style="z-index: 553;" name="parallax"
                   class="parallax-title"><?php echo $this->getTitle(); ?></a>
            <?php endif; ?>
            <?php if ($this->getButton()): ?>
                <a name="parallaxMore" href="<?php echo $this->getData('url'); ?>" class="view-more"><?php echo $this->getButton() ?></a>
            <?php endif ?>
        </div>
        <?php if ($this->getImageSrc()): ?>
            <a class="d-block" href="<?php echo $this->getData('url'); ?>" name="parallaxImage">
                <div class="parallax-image-wrapper">
                    <div class="parallax-window"></div>
                </div>
            </a>
        <?php endif ?>
    </div>
</div>

<script>
    jQuery(function ($) {
        $('.parallax-window').parallax({
            naturalHeight: 500,
            imageSrc: '<?php echo $this->getImageSrc(); ?>',
            iosFix: true,
            androidFix: true,
            overScrollFix: true
        });
    });
</script>