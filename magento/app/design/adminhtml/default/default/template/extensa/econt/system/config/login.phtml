<?php
/**
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
?>
<?php
/**
 * @var $this Extensa_Econt_Block_Adminhtml_System_Config_Form_Login
 */
?>
<?php if ($this->getButtonDisabled()) : ?>
<p><?php echo Mage::helper('extensa_econt')->__('Все още не можете да влезете, тъй като инсталацията не е приключила.<br />Моля, изчакайте...'); ?></p>
<?php endif; ?>
<script type="text/javascript">
    <?php if ($this->getButtonDisabled()) : ?>
    setTimeout(function() {
        window.location = window.location;
    }, 60000);
    <?php endif; ?>

    //<![CDATA[
    function extensa_econt_login() {
        new Ajax.Request(
            '<?php echo $this->getLoginUrl(); ?>',
            {
                method:     'post',
                parameters: {
                    username: $F('carriers_extensa_econt_username'),
                    password: $F('carriers_extensa_econt_password'),
                    test    : $F('carriers_extensa_econt_test'),
                },
                onSuccess: function(transport) {
                    if (transport.responseText.isJSON()) {
                        response = transport.responseText.evalJSON();
                        if (response.success) {
                            window.location = window.location;
                        } else if (response.error || response.success) {
                            alert(response.message);
                        }
                    }
                },
                onFailure: function(){
                    alert('<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Възникна грешка! Моля, опитайте отново!')); ?>');
                }
            }
        );
    }
    //]]>
</script>
