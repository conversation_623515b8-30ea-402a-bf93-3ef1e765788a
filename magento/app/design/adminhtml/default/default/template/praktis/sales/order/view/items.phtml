<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2018 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_order = $this->getOrder() ?>
<script type="text/javascript">
    var orderItemComment = new OrderItemComment(
        '<?php echo Mage::getModel('adminhtml/url')->getUrl('adminhtml/order_item_comment/add'); ?>'
    );
</script>
<div class="grid np">
    <div class="hor-scroll">
        <table cellspacing="0" class="data order-tables">
            <col />
            <col />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <thead>
            <tr class="headings">
                <th><?php echo $this->helper('sales')->__('Product') ?></th>
                <th><?php echo $this->helper('sales')->__('Comment') ?></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Item Status') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Original Price') ?></span></th>
                <th><?php echo $this->helper('sales')->__('Price') ?></th>
                <th class="a-center"><?php echo $this->helper('sales')->__('Qty') ?></th>
                <th><?php echo $this->helper('sales')->__('Subtotal') ?></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Tax Amount') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Tax Percent') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Discount Amount') ?></span></th>
                <th class="last"><span class="nobr"><?php echo $this->helper('sales')->__('Row Total') ?></span></th>
            </tr>
            </thead>
            <?php $_items = $this->getItemsCollection() ?>
            <?php $i=0;foreach ($_items as $_item):?>
                <?php if ($_item->getParentItem()) continue; else $i++;?>
                <tbody class="<?php echo $i%2?'even':'odd' ?>">
                <?php echo $this->getItemHtml($_item) ?>
                <?php echo $this->getItemExtraInfoHtml($_item) ?>
                </tbody>
            <?php endforeach; ?>
        </table>
    </div>
</div>
<br />