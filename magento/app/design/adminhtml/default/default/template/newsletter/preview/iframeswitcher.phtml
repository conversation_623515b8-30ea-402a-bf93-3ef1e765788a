<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/* @var $this Mage_Core_Block_Template */
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $this->getLang() ?>" lang="<?php echo $this->getLang() ?>">
<head>
<?php echo $this->getChildHtml('head') ?>
<style type="text/css">
    html,body { height:100%; }
</style>
</head>
<body id="html-body" style="background:#fff;">
<div id="preview" class="cms-revision-preview">
    <div class="toolbar">
        <?php if (!Mage::app()->isSingleStoreMode()) :?>
        <p class="switcher">
            <?php echo $this->getChildHtml('store_switcher') ?>
            <button class="button" onclick="preview();"><span><span><span><?php echo $this->__('Preview'); ?></span></span></span></button>
        </p>
        <?php endif;?>
    </div>
    <iframe name="preview_iframe" id="preview_iframe" frameborder="0"></iframe>
    <?php echo $this->getChildHtml('preview_form'); ?>
</div>
<div id="loading-mask" style="display:none">
    <p class="loader" id="loading_mask_loader"><img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('adminhtml')->__('Loading...')) ?>"/><br/><?php echo Mage::helper('adminhtml')->__('Please wait...') ?></p>
</div>

<script type="text/javascript">
//<![CDATA[
var previewForm = $('preview_form');
var loadingMask = $('loading-mask');
var previewIframe = $('preview_iframe');

function preview() {
    previewForm.writeAttribute('target', previewIframe.readAttribute('id'));
    blockPreview();
    previewForm.submit();
}

function blockPreview() {
    var cumulativeOffset = $('preview').cumulativeOffset();
    $('loading-mask').setStyle({
        top:  ( cumulativeOffset.top ) + 'px',
        left: ( cumulativeOffset.left ) + 'px',
        width: $('preview').getWidth() + 'px',
        height: $('preview').getHeight() + 'px'
    });

    toggleSelectsUnderBlock($('loading-mask'), false);
    Element.show('loading-mask');
    setLoaderPosition();
}

function unBlockPreview() {
    toggleSelectsUnderBlock(loadingMask, true);
    Element.hide(loadingMask);
}

Event.observe(window, 'load', preview);
Event.observe(previewIframe, 'load', unBlockPreview);
//]]>
</script>
</body>
</html>
