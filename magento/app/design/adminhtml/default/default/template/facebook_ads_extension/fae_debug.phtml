<div id="wrapper-debug">
  <div id="facebook-header">
    <table><tbody>
      <tr><td><i class='logo'></i></td>
      </tbody></table>
  </div>
  <div id="fae-debug-flow">
    <button onclick="test();">Test</button>
  </div>
</div>

<script>
window.facebookAdsExtensionAjax = {
  setDiaSettingId: '<?php echo $this->getDiaSettingIdAjaxRoute() ?>'
 ,setPixelId: '<?php echo $this->getPixelAjaxRoute() ?>'
 ,setStoreId: '<?php echo $this->getStoreAjaxRoute() ?>'
 ,generateFeedNow: '<?php echo $this->getFeedGenerateNowAjaxRoute() ?>'
 ,setMsgerChatSetup: '<?php echo $this->getMsgerChatSetupAjaxRoute() ?>'
 ,debug: '<?php echo $this->getDebugRoute() ?>'
 ,debugAjax: '<?php echo $this->getDebugAjaxRoute() ?>'
};
window.test = {};
</script>

<script>
window.test.enableFeed = '<?php echo $this->enableFeedNOW() ?>';
</script>

<script>
window.test.feedWritePermission = '<?php echo $this->checkFeedWriteError() ?>';
</script>

<script>
window.test.pixelId = '<?php echo $this->fetchPixelId() ?>';
</script>

<script>
 window.test.pixel_install_time = '<?php echo $this->getPixelInstallTime() ?>';
</script>

<script>
window.test.baseUrl = '<?php echo $this->getStoreBaseUrl() ?>';
</script>

<script>
window.test.baseCurrency = '<?php echo $this->fetchStoreBaseCurrency() ?>';
</script>

<script>
window.test.timezoneId= <?php echo $this->fetchStoreTimezone() ?>;
</script>

<script>
window.test.storeName= '<?php echo $this->fetchStoreName() ?>';
</script>

<script>
window.test.feedEnabled= <?php echo $this->fetchFeedSetupEnabled() === 1 ? 'true' : 'false' ?>;
</script>

<script>
window.test.feedformat = '<?php echo $this->fetchFeedSetupFormat() ?>';
</script>

<script>
window.test.totalVisibleProducts: <?php echo FacebookAdsExtension::getTotalVisibleProducts($this->getSelectedStore())?>;
</script>

<script>
window.test.defaultStoreId: '<?php echo $this->getSelectedStore() ?>';
</script>

<script>
window.test.stores = '<?php echo json_encode($this->getStores()) ?>';
</script>

<script>
window.test.settingid = <?php echo $this->getDiaSettingId() ?>;
</script>

<script>
window.test.feedUrl: '<?php echo $this->getFeedUrl() ?>';
</script>

<script>
window.test.feedPingUrl: '<?php echo FacebookAdsExtension::getFeedPingUrl() ?>';
</script>
