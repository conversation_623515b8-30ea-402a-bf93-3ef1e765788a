<?php
/**
 * MageWorx
 * Admin Order Editor extension
 *
 * @category   MageWorx
 * @package    MageWorx_OrdersEdit
 * @copyright  Copyright (c) 2016 MageWorx (http://www.mageworx.com/)
 */
?>
<?php if($this->hasMethods()): ?>
    <form id="ordersedit_edit_form">
        <div id="order-billing_method_form">
            <dl class="payment-methods">
                <?php
                $_methods       = $this->getMethods();
                $_methodsCount  = count($_methods);
                $_counter = 0;
                ?>
                <?php foreach ($_methods as $_method): $_code = $_method->getCode(); $_counter++; ?>
                    <dt>
                        <?php if ($_methodsCount > 1): ?>
                            <input id="p_method_<?php echo $_code ?>" value="<?php echo $_code ?>" type="radio" name="payment[method]" title="<?php echo $this->escapeHtml($_method->getTitle()) ?>" onclick="orderEdit.switchPaymentMethod('<?php echo $_code ?>')"<?php if($this->getSelectedMethodCode()==$_code): ?> checked="checked"<?php endif; ?> <?php if ($_counter == $_methodsCount) : ?>class="validate-one-required-by-name"<?php endif;?>/>
                        <?php else :?>
                            <span class="no-display"><input id="p_method_<?php echo $_code ?>" value="<?php echo $_code ?>" type="radio" name="payment[method]" checked="checked" /></span>
                        <?php endif;?>

                        <label for="p_method_<?php echo $_code ?>"><?php echo $this->escapeHtml($_method->getTitle()) ?></label>
                    </dt>
                    <dd>
                        <?php echo $this->getChildHtml('payment.method.'.$_code) ?>
                    </dd>
                <?php endforeach; ?>
            </dl>
        </div>
    </form>
    <script type="text/javascript">orderEdit.switchPaymentMethod('<?php echo $this->getSelectedMethodCode() ?>')</script>
<?php else: ?>
    <div><?php echo Mage::helper('sales')->__('No Payment Methods') ?></div>
<?php endif; ?>
