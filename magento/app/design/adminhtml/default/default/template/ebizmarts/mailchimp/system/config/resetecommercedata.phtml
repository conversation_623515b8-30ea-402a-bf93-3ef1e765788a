<script type="text/javascript">
    //<![CDATA[
    function resetecommerce() {
        new Ajax.Request('<?php echo $this->getAjaxCheckUrl() ?>', {
            method: 'get',
            onSuccess: function (transport) {

                if (transport.responseText == 1) {
                    alert('All remote ecommerce data was deleted.')
                }
                else {
                    alert('An error happened deleting remote ecommerce data.')
                }
            },
            onFailure: function () {
                alert('Something went wrong.')
            }
        });
    }
    //]]>
</script>

<?php echo $this->getButtonHtml(); ?>