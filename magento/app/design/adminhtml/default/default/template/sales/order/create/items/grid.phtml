<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Adminhtml_Block_Sales_Order_Create_Items_Grid
 */
?>

<?php $_items = $this->getItems() ?>
<?php if (empty($_items)): ?>
    <div class="grid" id="order-items_grid">
        <table cellspacing="0" class="data order-tables">
            <col />
            <col width="100" />
            <col width="40" />
            <col width="100" />
            <col width="80" />
            <col width="100" />
            <col width="80" />
            <thead>
                <tr class="headings">
                    <th class="no-link"><?php echo $this->helper('sales')->__('Product') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Price') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Qty') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Subtotal') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Discount') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Row Subtotal') ?></th>

                    <th class="no-link last"><?php echo $this->helper('sales')->__('Action') ?></th>
                </tr>
            </thead>
            <tbody>
                <tr class="even">
                    <td class="empty-text a-center" colspan="100"><?php echo $this->helper('sales')->__('No ordered items') ?></td>
                </tr>
            </tbody>
        </table>
    </div>
<?php else: ?>
<div>
    <?php if(count($_items)>10): ?>
    <p class="a-right">
        <?php echo $this->getButtonHtml($this->helper('sales')->__('Update Items and Qty\'s'),'order.itemsUpdate()'); ?>
    </p>
    <?php endif; ?>
    <div class="grid" id="order-items_grid">
        <table cellspacing="0" class="data order-tables">
            <col />
            <col width="100" />
            <col width="100" />
            <col width="40"  />
            <col width="100" />
            <col width="80"  />
            <col width="100" />
            <col width="80" />
            <thead>
                <tr class="headings">
                    <th class="no-link" colspan="2"><?php echo $this->helper('sales')->__('Product') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Price') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Qty') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Subtotal') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Discount') ?></th>
                    <th class="no-link"><?php echo $this->helper('sales')->__('Row Subtotal') ?></th>

                    <th class="no-link last"><?php echo $this->helper('sales')->__('Action') ?></th>
                </tr>
            </thead>
            <tfoot>
                <tr>
                    <td class="a-left" colspan="2"><?php echo $this->helper('sales')->__('Total %d product(s)', count($_items)) ?></td>
                    <td colspan="2" class="a-right"><?php echo $this->helper('sales')->__('Subtotal:') ?></td>
                    <td class="price"><strong><?php echo $this->formatPrice($this->getSubtotal()) ?></strong></td>
                    <td class="price"><strong><?php echo $this->formatPrice($this->getDiscountAmount()) ?></strong></td>
                    <td class="price"><strong><?php echo $this->formatPrice($this->getSubtotalWithDiscount()) ?></strong></td>
                    <td colspan="2">&nbsp;</td>
                </tr>
            </tfoot>
                <?php $i=0 ?>
                <?php foreach ($_items as $_item):$i++ ?>
                <tbody class="<?php echo ($i%2)?'even':'odd' ?>">
                    <tr>
                        <td class="first">
                            <h5 class="title"><span id="order_item_<?php echo $_item->getId() ?>_title"><?php echo $this->escapeHtml($_item->getName()) ?></span></h5>
                            <div>
                                <strong><?php echo $this->helper('sales')->__('SKU') ?>:</strong>
                                <?php echo implode('<br />', Mage::helper('catalog')->splitSku($this->escapeHtml($_item->getSku()))); ?>
                            </div>
                            <?php if($_item->getMessage(false)): ?>
                                <?php foreach ($_item->getMessage(false) as $message): ?>
                                <div class="<?php if($_item->getHasError()): ?>error<?php else: ?>notice<?php endif; ?>">
                                    <div style="font-size:95%"><?php echo Mage::helper('core')->escapeHtml($message); ?></div>
                                </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </td>
                        <td class="a-center v-middle">
                            <?php echo $this->getConfigureButtonHtml($_item) ?>
                        </td>
                        <td class="price">




    <?php if ($this->helper('tax')->displayCartPriceExclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
        <?php endif; ?>
        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
            <?php echo $this->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()); ?>
        <?php else: ?>
            <?php echo $this->formatPrice($_item->getCalculationPrice()) ?>
        <?php endif; ?>


        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <br />
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>


    <?php if ($this->helper('tax')->displayCartPriceInclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <br /><span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
        <?php endif; ?>
        <?php $_incl = $this->helper('checkout')->getPriceInclTax($_item); ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?>
            <?php else: ?>
                <?php echo $this->formatPrice($_incl-$_item->getWeeeTaxDisposition()) ?>
            <?php endif; ?>
        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount_incl_tax'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>:<br /> <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>




                            <?php $_isCustomPrice = $this->usedCustomPriceForItem($_item) ?>
                            <?php if($_tier = $this->getTierHtml($_item)): ?>
                            <div id="item_tier_block_<?php echo $_item->getId() ?>"<?php if ($_isCustomPrice): ?> style="display:none"<?php endif; ?>>
                                <a href="#" onclick="$('item_tier_<?php echo $_item->getId() ?>').toggle();return false;"><?php echo $this->helper('sales')->__('Tier Pricing') ?></a>
                                <div style="display:none" id="item_tier_<?php echo $_item->getId() ?>"><?php echo $_tier ?></div>
                            </div>
                            <?php endif; ?>
                            <?php if ($this->canApplyCustomPrice($_item)): ?>
                            <div class="nobr">
                            <input type="checkbox" id="item_use_custom_price_<?php echo $_item->getId() ?>"<?php if ($_isCustomPrice): ?> checked="checked"<?php endif; ?> onclick="order.toggleCustomPrice(this, 'item_custom_price_<?php echo $_item->getId() ?>', 'item_tier_block_<?php echo $_item->getId() ?>');"/>
                            <label class="normal" for="item_use_custom_price_<?php echo $_item->getId() ?>"><?php echo $this->helper('sales')->__('Custom Price') ?>*</label>
                            </div>
                            <?php endif; ?>
                            <input id="item_custom_price_<?php echo $_item->getId() ?>" name="item[<?php echo $_item->getId() ?>][custom_price]" value="<?php echo sprintf("%.2f", $this->getOriginalEditablePrice($_item))?>"<?php if (!$_isCustomPrice): ?> style="display:none" disabled="disabled"<?php endif; ?> class="input-text item-price"/>
                        </td>
                        <td><input name="item[<?php echo $_item->getId() ?>][qty]" class="input-text item-qty" value="<?php echo $_item->getQty()*1 ?>" maxlength="12" /></td>
                        <td class="price">




    <?php if ($this->helper('tax')->displayCartPriceExclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
        <?php endif; ?>
        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
            <?php echo $this->formatPrice($_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?>
        <?php else: ?>
            <?php echo $this->formatPrice($_item->getRowTotal()) ?>
        <?php endif; ?>


        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <br />
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>


    <?php if ($this->helper('tax')->displayCartPriceInclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <br /><span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
        <?php endif; ?>
        <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
            <?php else: ?>
                <?php echo $this->formatPrice($_incl-$_item->getWeeeTaxRowDisposition()) ?>
            <?php endif; ?>
        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>:<br /> <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>



                        </td>
                        <td class="price">
                            <?php echo $this->formatPrice(-$_item->getDiscountAmount()) ?><br />
                            <input id="item_use_discount_<?php echo $_item->getId() ?>" name="item[<?php echo $_item->getId() ?>][use_discount]"<?php if (!$_item->getNoDiscount()): ?>checked="checked"<?php endif; ?> value="1" type="checkbox" />
                            <label for="item_use_discount_<?php echo $_item->getId() ?>" class="normal"><?php echo $this->helper('sales')->__('Apply') ?></label>
                        </td>
                        <td class="price">


    <?php if ($this->helper('tax')->displayCartPriceExclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($_item->getIsPriceInclTax()): ?>
            <?php $_rowTotalLessDiscount = $_item->getRowTotalInclTax() - $_item->getDiscountAmount()
                                    - ($_item->getTaxAmount() - Mage::helper('weee')->getTotalRowTaxAppliedForWeeeTax($_item)) ?>
        <?php else: ?>
            <?php $_rowTotalLessDiscount = $_item->getRowTotal() - $_item->getDiscountAmount() ?>
        <?php endif; ?>

        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
        <?php endif; ?>
        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
            <?php echo $this->formatPrice(max(0, $_rowTotalLessDiscount+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition())); ?>
        <?php else: ?>
            <?php echo $this->formatPrice(max(0, $_rowTotalLessDiscount)) ?>
        <?php endif; ?>

        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <br />
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->formatPrice($_item->getCalculationPrice()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>


    <?php if ($this->helper('tax')->displayCartPriceInclTax($this->getStore()) || $this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
        <?php if ($this->helper('tax')->displayCartBothPrices($this->getStore())): ?>
            <br /><span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
        <?php endif; ?>

        <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item) - $_item->getDiscountTaxCompensation() - $_item->getDiscountAmount(); ?>

        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
            <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
        <?php else: ?>
            <?php echo $this->formatPrice($_incl-$_item->getWeeeTaxRowDisposition()) ?>
        <?php endif; ?>
        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
            <br />
            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></small></span><br />
                <?php endforeach; ?>
            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                <small>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->formatPrice($tax['row_amount_incl_tax'],true,true); ?></span><br />
                <?php endforeach; ?>
                </small>
            <?php endif; ?>

            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                <span class="nobr"><?php echo Mage::helper('weee')->__('Total incl. tax'); ?>:<br /> <?php echo $this->formatPrice($_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>

                        </td>
                        <td class="last">
                            <select name="item[<?php echo $_item->getId() ?>][action]" style="width:100px;">
                                <option value=""></option>
                                <option value="remove"><?php echo $this->helper('sales')->__('Remove') ?></option>
                                <?php if($this->getCustomerId() && $this->getMoveToCustomerStorage()): ?>
                                    <option value="cart"><?php echo $this->helper('sales')->__('Move to Shopping Cart') ?></option>
                                    <?php if ($this->isMoveToWishlistAllowed($_item)): ?>
                                        <?php $wishlists = $this->getCustomerWishlists();?>
                                        <?php if (count($wishlists) <= 1):?>
                                            <option value="wishlist"><?php echo $this->helper('sales')->__('Move to Wishlist') ?></option>
                                        <?php else: ?>
                                            <optgroup label="<?php echo $this->helper('sales')->__('Move to Wishlist') ?>">
                                                <?php foreach ($wishlists as $wishlist):?>
                                                    <option value="wishlist_<?php echo $wishlist->getId();?>"><?php echo $this->escapeHtml($wishlist->getName());?></option>
                                                <?php endforeach;?>
                                            </optgroup>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </select>
                        </td>
                    </tr>
                    <?php echo $this->getItemExtraInfo($_item)->toHtml(); ?>
                </tbody>
                <?php endforeach; ?>
        </table>
    </div>
    <table cellspacing="0" width="100%">
        <col width="50%" />
        <col width="50%" />
        <tr>
            <td class="a-left"><small><?php echo $this->getInclExclTaxMessage(); ?></small></td>
            <td class="a-right"><?php echo $this->getButtonHtml($this->helper('sales')->__('Update Items and Qty\'s'),'order.itemsUpdate()'); ?></td>
        </tr>
    </table>
    <br />
    <div id="order-coupons"><?php echo $this->getChildHtml();?></div>
    <div class="clear"></div>
    <script type="text/javascript">order.itemsOnchangeBind()</script>
</div>

<?php if ($this->isGiftMessagesAvailable()) : ?>
<script type="text/javascript">
//<![CDATA[
    /**
     * Retrieve gift options tooltip content
     */
    function getGiftOptionsTooltipContent(itemId) {
        var contentLines = [];
        var headerLine = null;
        var contentLine = null;

        $$('#gift_options_data_' + itemId + ' .gift-options-tooltip-content').each(function (element) {
            if (element.down(0)) {
                headerLine = element.down(0).innerHTML;
                contentLine = element.down(0).next().innerHTML;
                if (contentLine.length > 30) {
                    contentLine = contentLine.slice(0,30) + '...';
                }
                contentLines.push(headerLine + ' ' + contentLine);
            }
        });
        return contentLines.join('<br/>');
    }
    giftOptionsTooltip.setTooltipContentLoaderFunction(getGiftOptionsTooltipContent);

//]]>
</script>
<?php endif; ?>
<?php endif; ?>
