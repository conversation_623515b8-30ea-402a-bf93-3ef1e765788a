<?php

/**
 * Nwdthemes Revolution Slider Extension
 *
 * @package     Revslider
 * <AUTHOR> <<EMAIL>>
 * @link		http://nwdthemes.com/
 * @copyright   Copyright (c) 2014. Nwdthemes
 * @license     http://themeforest.net/licenses/terms/regular
 */

?>
<script type="text/javascript">
	var thickboxL10n = {
		"next":				"<?php echo $this->__('Next >'); ?>",
		"prev":				"<?php echo $this->__('< Prev'); ?>",
		"image":			"<?php echo $this->__('Image'); ?>",
		"of":				"<?php echo $this->__('of'); ?>",
		"close":			"<?php echo $this->__('Close'); ?>",
		"noiframes":		"<?php echo $this->__('This feature requires inline frames. You have iframes disabled or your browser does not support them.'); ?>",
		"loadingAnimation":	"<?php echo $this->getSkinUrl('js/nwdthemes/revslider/thickbox/loadingAnimation.gif'); ?>"
	};
</script>
