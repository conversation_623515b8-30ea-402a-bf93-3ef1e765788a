<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * "Grant Access" button
 *
 * @var $this Mage_Oauth_Block_Authorize
 */
?>
<div class="login-container">
    <div class="login-box">
        <div class="login-form">
            <h1><?php echo $this->__('Confirmation Of Authorization') ?></h1>
            <?php echo $this->getMessagesBlock()->toHtml() ?>
            <?php if (!$this->getHasException()): ?>
                <p><?php echo $this->__('Give the verifier code to application administrator') ?></p>
                <p style="font-size: 14px;"><?php echo $this->__('Verifier code: %s', $this->getVerifier()) ?></p>
            <?php endif; ?>
        </div>
        <p class="legal"><?php echo $this->__('Magento is a trademark of Magento Inc. Copyright &copy; %s Magento Inc.', date('Y')) ?></p>
        <div class="bottom"></div>
    </div>
</div>
