<?php
/** @var PFG_Zeron_Helper_InventoryFile $inventoryHelper */
$inventoryHelper = Mage::helper('pfgzeron/inventoryFile');
/** @var Mage_Catalog_Model_Product $_product */
$_product = Mage::registry('current_product');
?>

<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?= Mage::helper('core')->__('Прикачени файлове'); ?></h4>
    </div>
    <div class="fieldset fieldset-wide" id="group_fields453">
        <div class="col text-center border-right product-label-wrapper">
            <h3><?= Mage::helper('core')->__('Енергиен клас: ') ?></h3>
            <?php $entity = $inventoryHelper->getEnergyLevelEntity($_product->getSku()) ?>
            <?php if($entity->getId() > 0): ?>
                <a target="_blank"
                   href="<?= $entity->getFileUrl() ?>">
                    <img src="<?= Mage::getDesign()->getSkinBaseUrl(
                        ['_package' => 'praktis', '_theme' => 'default', '_area'=>'frontend'])
                    . 'images/energy-label-' . mb_strtolower($entity->getEnergyClass()) . '.png' ?>"
                         alt="<?= $this->__('Energy Label') ?>">
                </a>
            <?php else: ?>
                <a target="_blank"
                   href="#">
                    <?= $this->__('Няма енергиен етикет') ?>
                </a>
            <?php endif; ?>
            <br>
            <br>
            <h3><?= Mage::helper('core')->__('Продуктов фиш: ') ?></h3>
            <?php $entity = $inventoryHelper->getProductSlipEntity($_product->getSku()) ?>
            <?php if($entity->getId() > 0): ?>
                <a target="_blank" href="<?= $entity->getFileUrl() ?>">
                    <span class="d-block text-dark"><?= $this->__('Продуктов фиш') ?></span>
                </a>
            <?php else: ?>
                <a target="_blank" href="#" >
                    <?= $this->__('Няма продуктов фиш') ?>
                </a>
            <?php endif; ?>
        </div>
    </div>
</div>