<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @methods
 * - getValue($key, $default)
 * - getSelected($key, $value)
 * - getChecked($key)
 * - getAttributes()
 * - getMappings()
 * - getAddMapButtonHtml()
 * - getRemoveMapButtonHtml()
 */
?>

<script type="text/javascript">
//<![CDATA[
var profileImportOnly = ['profile_number_of_records', 'profile_decimal_separator'];
function showOption(select)
{
    select = $(select);
    for (var i = 0, l = select.options.length; i<l; i++) {
        $$('.'+select.id+'_'+select.options[i].value).each(function (el) {
            el.style.display = select.selectedIndex==i ? '' : 'none';
        });
    }

}

function addFieldMapping()
{
    var entityType = $('profile_entity_type').value;
    Element.insert($('map_container_'+entityType), {bottom: $('map_template_'+entityType).innerHTML});
}

function removeFieldMapping(button)
{
    Element.remove(button.parentNode);
}

function setMapFileField(select)
{
    select.parentNode.getElementsByTagName('input')[0].value = select.value;
}

function toggleSelectOption(type, source, target, sourceValue, targetValue, targetText)
{
    source = $(source);
    target = $(target);

    var i, ex, option, present;
    var selectValue = source.options[source.selectedIndex].value;

    if('remove'==type && selectValue==sourceValue || 'add'==type && selectValue!=sourceValue) {
        for(i=0;i<target.options.length;i++){
            if(target.options[i].value==targetValue){
                target.remove(i);
                break;
            }
        }
    } else {
        for(i=0;i<target.options.length;i++){
            if(target.options[i].value==targetValue){
                present = true;
                break;
            } else {
                present = false;
            }
        }
        if(!present){
            option = document.createElement("OPTION");
            option.text = targetText;
            option.value = targetValue;
            try {
                target.add(option, null); // standards compliant; doesn't work in IE
            } catch(ex) {
                target.add(option); // IE only
            }
        }
    }
    showOption(target);
}

function exportUrlField()
{
    var urlFieldEl = $('profile_add_url_field').up();
    var entityType = $('profile_entity_type').value;
    var direction  = $('profile_direction').value;
    var storeId    = $('profile_store_id').value;

    if (entityType == 'product' && direction == 'export' && storeId > 0) {
        urlFieldEl.show();
    }
    else {
        urlFieldEl.hide();
    }
}

function changeEntityType()
{
    //toggleSelectOption('remove', 'profile_entity_type', 'profile_direction', 'customer', 'import', '<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Import')) ?>');
    //toggleSelectOption('remove', 'profile_entity_type', 'profile_store_id', 'customer', '0', '<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Default Values')) ?>');
    //showOption('profile_direction');
    changeDirection();
}


function changeDirection()
{
    toggleSelectOption('remove', 'profile_direction', 'profile_data_transfer', 'export', 'interactive', '<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Interactive')) ?>');
    //showOption('profile_data_transfer');
    if ($('profile_direction').value == 'import') {
        profileImportOnly.each(function(id){if ($(id))$(id).up(1).show();});
    }
    else {
        profileImportOnly.each(function(id){if ($(id))$(id).up(1).hide();});
    }
    exportUrlField();
}

function updateRun(select)
{
    if ($('file_list') != null){
        if ($(select).value=='interactive') {
            $('file_list').show();
        } else {
            $('file_list').hide();
        }
    }
}

Event.observe(window, 'load', function(){
    if($('profile_data_transfer')) {
        updateRun('profile_data_transfer');
    }
});
//]]>
</script>

<div id="profile-generator" class="entry-edit profile-generator">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("Profile Information") ?></h4>
    </div>
    <fieldset>
        <legend><?php echo $this->__("Profile Information") ?></legend>
        <span class="field-row">
            <label for="profile_name"><?php echo $this->__("Name:") ?> <span class="required">*</span></label>
            <input class="required-entry required-entry input-text" id="profile_name" name="name" value="<?php echo $this->getValue('name') ?>"/>
        </span>
        <span class="field-row">
            <label for="profile_entity_type"><?php echo $this->__("Entity type:") ?></label>
            <select id="profile_entity_type" name="entity_type" onchange="showOption(this);changeEntityType();" class="option-control">
                <option value="product" <?php echo $this->getSelected('entity_type', 'product') ?>><?php echo $this->__("Products") ?></option>
                <option value="customer" <?php echo $this->getSelected('entity_type', 'customer') ?>><?php echo $this->__("Customers") ?></option>
            </select>
        </span>
        <span class="field-row">
            <label for="profile_direction"><?php echo $this->__("Direction:") ?></label>
            <select id="profile_direction" name="direction" onchange="showOption(this);changeDirection();" class="option-control">
                <option value="import" <?php echo $this->getSelected('direction', 'import') ?>><?php echo $this->__("Import") ?></option>
                <option value="export" <?php echo $this->getSelected('direction', 'export') ?>><?php echo $this->__("Export") ?></option>
            </select>
        </span>
        <span class="field-row">
            <label for="profile_store_id"><?php echo $this->__("Store:") ?></label>
            <span class="with-tip">
                <select id="profile_store_id" name="store_id" onchange="exportUrlField();">
                <option value="0"><?php echo $this->__('Default (Admin) Values') ?></option>
                    <?php foreach ($this->getWebsiteCollection() as $_website): ?>
                        <?php $_websiteShow=false; ?>
                        <?php foreach ($this->getGroupCollection() as $_group): ?>
                            <?php if ($_website->getId() != $_group->getWebsiteId()) continue; ?>
                            <?php $_groupShow=false; ?>
                            <?php foreach ($this->getStoreCollection() as $_store): ?>
                                <?php if ($_group->getId() != $_store->getGroupId()) continue; ?>
                                <?php if (!$_websiteShow): ?>
                                    <?php $_websiteShow=true; ?>
                                    <optgroup label="<?php echo Mage::helper('core')->quoteEscape($_website->getName()) ?>"></optgroup>
                                <?php endif; ?>
                                <?php if (!$_groupShow): ?>
                                    <?php $_groupShow=true; ?>
                                    <optgroup label="&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($_group->getName()); ?>">
                                <?php endif; ?>
                                <option value="<?php echo $_store->getId() ?>" <?php echo $this->getSelected('store_id', $_store->getId()) ?>>&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($_store->getName()); ?></option>
                            <?php endforeach; ?>
                            <?php if ($_groupShow): ?>
                                </optgroup>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                </select>
                <small class="profile_direction_import profile_entity_type_product">
                        <?php echo $this->__("(Products will be added/updated to this store if 'store' column is blank or missing in the import file.)") ?>
                </small>
            </span>
        </span>
        <span class="field-row">
            <label for="profile_add_url_field"><?php echo $this->__("Add Field with URL:") ?></label>
            <select id="profile_add_url_field" name="gui_data[export][add_url_field]" class="option-control">
                <option value="0"<?php echo $this->getSelected('gui_data/export/add_url_field', 0) ?>><?php echo $this->__("No") ?></option>
                <option value="1"<?php echo $this->getSelected('gui_data/export/add_url_field', 1) ?>><?php echo $this->__("Yes") ?></option>
            </select>
        </span>
        <span class="field-row">
            <label for="profile_number_of_records"><?php echo $this->__("Number of records:") ?></label>
            <span class="with-tip">
                <input class="required-entry validate-number input-text" id="profile_number_of_records" name="gui_data[import][number_of_records]" value="<?php echo (($numbers = $this->getValue('gui_data/import/number_of_records')) ? $numbers : 1) ?>"/>
                <small><?php echo $this->__("(You have to increase php memory_limit before changing this value)") ?></small>
            </span>
        </span>
        <span class="field-row">
            <label for="profile_decimal_separator"><?php echo $this->__("Decimal separator:") ?></label>
            <span class="with-tip">
                <input class="required-entry input-text" id="profile_decimal_separator" name="gui_data[import][decimal_separator]" value="<?php echo (($separator = $this->getValue('gui_data/import/decimal_separator')) ? $separator : '.') ?>"/>
            </span>
        </span>
    </fieldset>

    <div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("File Information") ?></h4>
    </div>
    <fieldset>
        <legend><?php echo $this->__("File Information") ?></legend>
        <span class="field-row">
            <label for="profile_data_transfer"><?php echo $this->__("Data transfer:") ?></label>
            <select id="profile_data_transfer" name="data_transfer" onchange="showOption(this);updateRun(this)" class="option-control">
                <option value="interactive" <?php echo $this->getSelected('data_transfer', 'interactive') ?>><?php echo $this->__("Interactive") ?></option>
                <option value="file" <?php echo $this->getSelected('data_transfer', 'file') ?>><?php echo $this->__("Local/Remote Server") ?></option>
            </select>
        </span>
        <!--
        <div class="profile_data_transfer_interactive">
            <span class="field-row">
                <label for="interactive_filename"><?php echo $this->__("Archive file name:") ?></label>
                <input class="input-text" id="interactive_filename" name="gui_data[interactive][filename]" value="<?php echo $this->getValue('gui_data/interactive/filename') ?>"/>
                <?php echo $this->__('(If left empty will be auto-generated)') ?>
            </span>
        </div>
        -->
        <div class="profile_data_transfer_file">
            <span class="field-row">
                <label for="file_type"><?php echo $this->__("Type:") ?></label>
                <select id="file_type" name="gui_data[file][type]" onchange="showOption(this)" class="option-control">
                    <option value="file" <?php echo $this->getSelected('gui_data/file/type', 'file') ?>><?php echo $this->__("Local Server") ?></option>
                    <option value="ftp" <?php echo $this->getSelected('gui_data/file/type', 'ftp') ?>><?php echo $this->__("Remote FTP") ?></option>
                </select>
            </span>
            <span class="field-row">
                <label for="file_filename"><?php echo $this->__("File name:") ?></label>
                <input class="input-text" id="file_filename" name="gui_data[file][filename]" value="<?php echo $this->getValue('gui_data/file/filename') ?>"/>
            </span>
            <span class="field-row">
                <label for="file_path"><?php echo $this->__("Path:") ?></label>
                <span class="with-tip">
                    <input class="input-text" id="file_path" name="gui_data[file][path]" value="<?php echo $this->getValue('gui_data/file/path') ?>"/>
                    <small class="file_type_file">
                        <?php echo $this->__('(For Type "Local Server" need to use relative path to Magento install var/export or var/import, e.g. var/export, var/import, var/export/some/dir, var/import/some/dir)') ?>
                    </small>
                </span>
            </span>
            <div class="file_type_ftp">
                <span class="field-row">
                    <label for="file_host"><?php echo $this->__("FTP Host[:Port]") ?></label>
                    <input class="input-text" id="file_host" name="gui_data[file][host]" value="<?php echo $this->getValue('gui_data/file/host') ?>"/>
                </span>
                <span class="field-row">
                    <label for="file_user"><?php echo $this->__("User name") ?></label>
                    <input class="input-text" id="file_user" name="gui_data[file][user]" value="<?php echo $this->getValue('gui_data/file/user') ?>"/>
                </span>
                <span class="field-row">
                    <label for="file_password"><?php echo $this->__("Password") ?></label>
                    <input class="input-text" type="password" id="io_password" name="gui_data[file][password]" value="<?php echo $this->getValue('gui_data/file/password') ?>"/>
                </span>
                <span class="field-row">
                    <label for="file_mode"><?php echo $this->__("File mode") ?></label>
                    <select id="file_mode" name="gui_data[file][file_mode]" onchange="showOption(this)" class="option-control">
                        <option value="<?php echo FTP_BINARY ?>" <?php echo $this->getSelected('gui_data/file/file_mode', FTP_BINARY) ?>><?php echo $this->__('BINARY') ?></option>
                        <option value="<?php echo FTP_ASCII ?>" <?php echo $this->getSelected('gui_data/file/file_mode', FTP_ASCII) ?>><?php echo $this->__('ASCII') ?></option>
                    </select>
                </span>
                <span class="field-row">
                    <label for="file_passive"><?php echo $this->__("Passive mode") ?></label>
                    <select id="file_passive" name="gui_data[file][passive]" onchange="showOption(this)" class="option-control">
                        <option value="" <?php echo $this->getSelected('gui_data/file/passive', false) ?>><?php echo $this->__("No") ?></option>
                        <option value="true" <?php echo $this->getSelected('gui_data/file/passive', true) ?>><?php echo $this->__("Yes") ?></option>
                    </select>
                </span>
            </div>
        </div>
    </fieldset>

    <div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("Data Format") ?></h4>
    </div>
    <fieldset>
        <legend><?php echo $this->__("Data Format") ?></legend>
        <span class="field-row">
            <label for="parse_type"><?php echo $this->__("Type:") ?></label>
            <select id="parse_type" name="gui_data[parse][type]" onchange="showOption(this)" class="option-control">
                <option value="excel_xml" <?php echo $this->getSelected('gui_data/parse/type', 'excel_xml') ?>><?php echo $this->__("MS Excel XML") ?></option>
                <option value="csv" <?php echo $this->getSelected('gui_data/parse/type', 'csv') ?>><?php echo $this->__("CSV / Tab separated") ?></option>
            </select>
        </span>
        <div class="parse_type_excel_xml">
            <span class="field-row">
                <label for="parse_single_sheet"><?php echo $this->__("Spreadsheet Name:") ?></label>
                <span class="with-tip">
                    <input class="input-text" id="parse_single_sheet" name="gui_data[parse][single_sheet]" value="<?php echo $this->getValue('gui_data/parse/single_sheet') ?>"/>
                    <small><?php echo $this->__("(Leave empty for first spreadsheet)") ?></small>
                </span>
            </span>
        </div>
        <div class="parse_type_csv">
            <span class="field-row">
                <label for="parse_delimiter"><?php echo $this->__("Value Delimiter:") ?></label>
                <input class="input-text" style="width:3em" id="parse_delimiter" name="gui_data[parse][delimiter]" value="<?php echo $this->getValue('gui_data/parse/delimiter', ',') ?>"/>
                <small><?php echo $this->__("(\\t for tab)") ?></small>
            </span>
            <span class="field-row">
                <label for="parse_enclose"><?php echo $this->__("Enclose Values In:") ?></label>
                <input class="input-text" style="width:3em" id="parse_enclose" name="gui_data[parse][enclose]" value="<?php echo $this->getValue('gui_data/parse/enclose', '', '"') ?>"/>
                <small><?php echo $this->__("Warning! Empty value can cause problems with CSV format.") ?></small>
            </span>
        </div>
        <span class="field-row">
            <label for="parse_fieldnames"><?php echo $this->__("Original Magento attribute names in first row:") ?></label>
            <span class="with-tip">
                <select id="parse_fieldnames" name="gui_data[parse][fieldnames]" onchange="showFeildMapping();showOption(this)" class="option-control">
                    <option value="" <?php echo $this->getSelected('gui_data/parse/fieldnames', false) ?>><?php echo $this->__("No") ?></option>
                    <option value="true" <?php echo $this->getSelected('gui_data/parse/fieldnames', true) ?>><?php echo $this->__("Yes") ?></option>
                </select>
                <small class="profile_direction_import">
                    <span class="parse_fieldnames_">
                        <?php echo $this->__("(When 'No', only mapped fields will be imported. When mapping, use 'column1', 'column2', etc.)") ?>
                    </span>
                </small>
            </span>
        </span>
        <div class="profile_direction_export">
            <span class="field-row">
                <label for="map_only_specified"><?php echo $this->__("Export:") ?></label>
                <select id="map_only_specified" name="gui_data[map][only_specified]" onchange="showFeildMapping();showOption(this)">
                    <option value="" <?php echo $this->getSelected('gui_data/map/only_specified', false) ?>><?php echo $this->__("All fields") ?></option>
                    <option value="true" <?php echo $this->getSelected('gui_data/map/only_specified', true) ?>><?php echo $this->__("Only mapped fields") ?></option>
                </select>
            </span>
        </div>
    </fieldset>

    <div id="__fieldmapping">
    <div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("Field Mapping") ?></h4>
    </div>
    <fieldset>
        <legend><?php echo $this->__("Field Mapping") ?></legend>
        <?php foreach (array('product', 'customer') as $_entityType): ?>
            <div class="profile_entity_type_<?php echo $_entityType ?>">
                <div id="map_template_<?php echo $_entityType ?>" style="display:none">
                    <span class="field-row">
                        <?php echo $this->__("In Database:") ?> <select name="gui_data[map][<?php echo $_entityType ?>][db][]" onchange="setMapFileField(this)">
                            <?php
                                $fieldMappingInDatabase = $this->getAttributes($_entityType);
                                asort($fieldMappingInDatabase);
                                foreach ($fieldMappingInDatabase as $_value=>$_label): ?>
                                <option value="<?php echo $_value ?>"><?php echo $_label ?></option>
                            <?php endforeach ?>
                        </select>
                        &lt;--&gt;
                        <?php echo $this->__("In File:") ?> <input class="input-text" name="gui_data[map][<?php echo $_entityType ?>][file][]"/>
                        <?php echo $this->getRemoveMapButtonHtml() ?>
                    </span>
                </div>
                <div id="map_container_<?php echo $_entityType ?>">
                    <?php foreach ($this->getMappings($_entityType) as $_i=>$_dbField): ?>
                        <span class="field-row">
                            <?php echo $this->__("In Database:") ?> <select name="gui_data[map][<?php echo $_entityType ?>][db][]">
                            <?php foreach ($this->getAttributes($_entityType) as $_value=>$_label): ?>
                                <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/map/'.$_entityType.'/db/'.$_i, $_value) ?>><?php echo $_label ?></option>
                            <?php endforeach ?>
                            </select>
                            &lt;--&gt;
                            <?php echo $this->__("In File:") ?> <input class="input-text" name="gui_data[map][<?php echo $_entityType ?>][file][]" value="<?php echo $this->getValue('gui_data/map/'.$_entityType.'/file/'.$_i) ?>"/>
                            <?php echo $this->getRemoveMapButtonHtml() ?>
                        </span>
                    <?php endforeach ?>
                </div>
            </div>
        <?php endforeach ?>
        <?php echo $this->getAddMapButtonHtml() ?>
    </fieldset>
    </div>
    <div class="profile_direction_export">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("Export Filters") ?></h4>
        </div>
        <fieldset>
            <div class="profile_entity_type_product">
                <span class="field-row">
                    <label for="product_filter_name"><?php echo $this->__("Name:") ?></label>
                    <input class="input-text" id="product_filter_name" name="gui_data[product][filter][name]" value="<?php echo $this->getValue('gui_data/product/filter/name') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="product_filter_sku"><?php echo $this->__("SKU:") ?></label>
                    <input class="input-text" id="product_filter_sku" name="gui_data[product][filter][sku]" value="<?php echo $this->getValue('gui_data/product/filter/sku') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="product_filter_type"><?php echo $this->__("Type:") ?></label>
                    <select id="product_filter_type" name="gui_data[product][filter][type]">
                        <?php foreach ($this->getProductTypeFilterOptions() as $_value=>$_label): ?>
                            <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/product/filter/type', $_value) ?>><?php echo htmlspecialchars($_label) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
                <span class="field-row">
                    <label for="product_filter_attribute_set"><?php echo $this->__("Attribute Set Name:") ?></label>
                    <select id="product_filter_attribute_set" name="gui_data[product][filter][attribute_set]">
                        <?php foreach ($this->getProductAttributeSetFilterOptions() as $_value=>$_label): ?>
                            <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/product/filter/attribute_set', $_value) ?>><?php echo htmlspecialchars($_label) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
                <span class="field-row">
                    <label for="product_filter_price_from"><?php echo $this->__("Price:") ?></label>
                    <input class="input-text" style="width:5em" id="product_filter_price_from" name="gui_data[product][filter][price][from]" value="<?php echo $this->getValue('gui_data/product/filter/price/from') ?>"/> <?php echo $this->__('to') ?>
                    <input class="input-text" style="width:5em" id="product_filter_price_to" name="gui_data[product][filter][price][to]" value="<?php echo $this->getValue('gui_data/product/filter/price/to') ?>"/>
                </span>
                <span class="field-row">
                    <label for="product_filter_qty_from"><?php echo $this->__("Stock Quantity:") ?></label>
                    <input class="input-text" style="width:5em" id="product_filter_qty_from" name="gui_data[product][filter][qty][from]" value="<?php echo $this->getValue('gui_data/product/filter/qty/from') ?>"/> <?php echo $this->__('to') ?>
                    <input class="input-text" style="width:5em" id="product_filter_qty_to" name="gui_data[product][filter][qty][to]" value="<?php echo $this->getValue('gui_data/product/filter/qty/to') ?>"/>
                </span>
                <span class="field-row">
                    <label for="product_filter_visibility"><?php echo $this->__("Visibility:") ?></label>
                    <select id="product_filter_visibility" name="gui_data[product][filter][visibility]">
                        <?php foreach ($this->getProductVisibilityFilterOptions() as $_value=>$_label): ?>
                            <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/product/filter/visibility', $_value) ?>><?php echo htmlspecialchars($_label) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
                <span class="field-row">
                    <label for="product_filter_status"><?php echo $this->__("Status:") ?></label>
                    <select id="product_filter_status" name="gui_data[product][filter][status]">
                        <?php foreach ($this->getProductStatusFilterOptions() as $_value=>$_label): ?>
                            <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/product/filter/status', $_value) ?>><?php echo htmlspecialchars($_label) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
            </div>
            <div class="profile_entity_type_customer">
                <span class="field-row">
                    <label for="customer_filter_firstname"><?php echo $this->__("First Name:") ?></label>
                    <input class="input-text" id="customer_filter_firstname" name="gui_data[customer][filter][firstname]" value="<?php echo $this->getValue('gui_data/customer/filter/firstname') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="customer_filter_lastname"><?php echo $this->__("Last Name:") ?></label>
                    <input class="input-text" id="customer_filter_lastname" name="gui_data[customer][filter][lastname]" value="<?php echo $this->getValue('gui_data/customer/filter/lastname') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="customer_filter_email"><?php echo $this->__("Email:") ?></label>
                    <input class="input-text" id="customer_filter_email" name="gui_data[customer][filter][email]" value="<?php echo $this->getValue('gui_data/customer/filter/email') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="customer_filter_group"><?php echo $this->__("Group:") ?></label>
                    <select id="customer_filter_group" name="gui_data[customer][filter][group]">
                        <?php foreach ($this->getCustomerGroupFilterOptions() as $_value=>$_label): ?>
                            <option value="<?php echo $_value ?>" <?php echo $this->getSelected('gui_data/customer/filter/group', $_value) ?>><?php echo htmlspecialchars($_label) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
                <span class="field-row">
                    <label for="customer_filter_adressType"><?php echo $this->__("Address Type:") ?></label>
                    <select id="customer_filter_adressType" name="gui_data[customer][filter][adressType]" >
                            <option value="default_billing" <?php echo $this->getSelected('gui_data/customer/filter/adressType', 'default_billing') ?>><?php echo $this->__('Billing Address') ?></option>
                            <option value="default_shipping" <?php echo $this->getSelected('gui_data/customer/filter/adressType', 'default_shipping') ?>><?php echo $this->__('Shipping Address') ?></option>
                    </select>
                </span>
                <span class="field-row">
                    <label for="customer_filter_telephone"><?php echo $this->__("Phone:") ?></label>
                    <input class="input-text" id="customer_filter_telephone" name="gui_data[customer][filter][telephone]" value="<?php echo $this->getValue('gui_data/customer/filter/telephone') ?>"/>
                    <?php echo $this->__('(Starting with)') ?>
                </span>
                <span class="field-row">
                    <label for="customer_filter_postcode"><?php echo $this->__("Zip/Postal Code:") ?></label>
                    <input class="input-text" id="customer_filter_postcode" name="gui_data[customer][filter][postcode]" value="<?php echo $this->getValue('gui_data/customer/filter/postcode') ?>"/>
                </span>
                <span class="field-row">
                    <label for="customer_filter_country"><?php echo $this->__("Country:") ?></label>
                    <select id="customer_filter_country" name="gui_data[customer][filter][country]">
                        <?php foreach ($this->getCountryFilterOptions() as $_value): ?>
                            <option value="<?php echo $_value['value'] ?>" <?php echo $this->getSelected('gui_data/customer/filter/country', $_value['value']) ?>><?php echo htmlspecialchars($_value['label']) ?></option>
                        <?php endforeach ?>
                    </select>
                </span>
                <span class="field-row">
                    <label for="customer_filter_region"><?php echo $this->__("State/Province:") ?></label>
                    <input class="input-text" id="customer_filter_region" name="gui_data[customer][filter][region]" value="<?php echo $this->getValue('gui_data/customer/filter/region') ?>"/>
                    <?php echo $this->__('(For US 2-letter state names)') ?>
                </span>
                <span class="field-row">
                    <label for="customer_filter_created_at_from"><?php echo $this->__("Customer Since:") ?></label>
                    <input class="input-text" style="width:5em" id="customer_filter_created_at_from" name="gui_data[customer][filter][created_at][from]" value="<?php echo $this->getValue('gui_data/customer/filter/created_at/from') ?>"/>
                    <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" alt="" class="v-middle" id="customer_filter_created_at_from_trig" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Date selector')) ?>" />
                     <?php echo $this->__('to') ?>
                    <input class="input-text" style="width:5em" id="customer_filter_created_at_to" name="gui_data[customer][filter][created_at][to]" value="<?php echo $this->getValue('gui_data/customer/filter/created_at/to') ?>"/>
                    <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>" alt="" class="v-middle" id="customer_filter_created_at_to_trig" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Date selector')) ?>" />
<script type="text/javascript">
    Calendar.setup({
        inputField : "customer_filter_created_at_from",
        ifFormat : "<?php echo $this->getShortDateFormat() ?>",
        button : "customer_filter_created_at_from_trig",
        align : "Bl",
        singleClick : true
    });
    Calendar.setup({
        inputField : "customer_filter_created_at_to",
        ifFormat : "<?php echo $this->getShortDateFormat() ?>",
        button : "customer_filter_created_at_to_trig",
        align : "Bl",
        singleClick : true
    });
</script>
                </span>
            </div>
        </fieldset>
    </div>

</div>

<script type="text/javascript">
//<![CDATA[
    $$('.option-control').each(showOption);
    changeEntityType();

    function showFeildMapping() {
        var direction = $('profile_direction').options[$('profile_direction').options.selectedIndex].value;
        var value = $('parse_fieldnames').options[$('parse_fieldnames').options.selectedIndex].value;
        var map = $('map_only_specified').options[$('map_only_specified').options.selectedIndex].value;

        if (direction == 'export') {
            if (map) {
                Element.show($('__fieldmapping'));
            } else {
                deleteAllAddedMappingFields();
                Element.hide($('__fieldmapping'));
            }
        } else {
            if (value) {
                Element.hide($('__fieldmapping'));
                deleteAllAddedMappingFields();
            } else {
                Element.show($('__fieldmapping'));
            }
        }

    }

    function deleteAllAddedMappingFields()
    {
        var type = $('profile_entity_type').options[$('profile_entity_type').options.selectedIndex].value;

        var elems = $('map_container_'+type).childElements();
        if (elems.length > 0) for (var idx in elems) {
            if (elems[idx] != undefined && elems[idx].tagName != undefined) {
                elems[idx].remove();
            }
        }
    }
    var direction = $('profile_direction').options[$('profile_direction').options.selectedIndex].value;
    if (direction == 'import') {
        if ($('parse_fieldnames').options[$('parse_fieldnames').options.selectedIndex].value) {
            Element.hide($('__fieldmapping'));
            deleteAllAddedMappingFields();
        } else {
            Element.show($('__fieldmapping'));
        }
    } else {
        var map = $('map_only_specified').options[$('map_only_specified').options.selectedIndex].value;
        if (map) {
            Element.show($('__fieldmapping'));
        } else {
            deleteAllAddedMappingFields();
            Element.hide($('__fieldmapping'));
        }
    }
//]]>
</script>
