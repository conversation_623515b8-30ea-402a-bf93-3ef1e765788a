"Active","Active"
"Add Attribute Mapping","Add Attribute Mapping"
"Add New Attribute","Add New Attribute"
"Add to Google Base","Add to Google Base"
"Are you sure?","Are you sure?"
"Attribute","Attribute"
"Attribute Set","Attribute Set"
"Attribute Set and Item Type","Attribute Set and Item Type"
"Attributes","Attributes"
"Attributes Mapping","Attributes Mapping"
"Attributes Set","Attributes Set"
"AuthSub","AuthSub"
"Available Products","Available Products"
"Base Currency should be set to %s for %s in system configuration. Otherwise item prices won't be correct in Google Base.","Base Currency should be set to %s for %s in system configuration. Otherwise item prices won't be correct in Google Base."
"Cannot update Google Base Item for Store '%s'","Cannot update Google Base Item for Store '%s'"
"Captcha confirmation error: %s","Captcha confirmation error: %s"
"Captcha has been confirmed.","Captcha has been confirmed."
"Catalog","Catalog"
"Clicks","Clicks"
"ClientLogin","ClientLogin"
"Confirm","Confirm"
"Current Mapping will be reloaded. Continue?","Current Mapping will be reloaded. Continue?"
"Custom attribute, no mapping","Custom attribute, no mapping"
"Delete","Delete"
"Delete Mapping","Delete Mapping"
"Edit Item Type","Edit Item Type"
"Edit Item Type ""%s""","Edit Item Type ""%s"""
"Error: %s","Error: %s"
"Expires","Expires"
"Google","Google"
"Google Base","Google Base"
"Google Base Attribute","Google Base Attribute"
"Google Base ID","Google Base ID"
"Google Base Item Type","Google Base Item Type"
"Google Base Item type","Google Base Item type"
"Google Base Items","Google Base Items"
"Google base","Google base"
"Hide","Hide"
"Hosted","Hosted"
"Hosted or Google","Hosted or Google"
"Impr.","Impr."
"Invalid Product Model for Google Base Item","Invalid Product Model for Google Base Item"
"Item Type was deleted","Item Type was deleted"
"Item Types","Item Types"
"Item model is not specified to delete Google Base entry.","Item model is not specified to delete Google Base entry."
"Items","Items"
"Manage Attribute Mapping","Manage Attribute Mapping"
"Manage Attributes","Manage Attributes"
"Manage Items","Manage Items"
"New Item Type","New Item Type"
"New ItemType","New ItemType"
"No","No"
"No items were deleted from Google Base","No items were deleted from Google Base"
"No items were published","No items were published"
"No items were saved as inactive items","No items were saved as inactive items"
"No products were added to Google Base","No products were added to Google Base"
"Object model is not specified to save Google Base entry.","Object model is not specified to save Google Base entry."
"Please, select Attribute Set and Google Item Type to load attributes","Please, select Attribute Set and Google Item Type to load attributes"
"Product Name","Product Name"
"Publish","Publish"
"Published","Published"
"Remove","Remove"
"Save Mapping","Save Mapping"
"Session expired during export. Please revise exported products and repeat the process if necessary.","Session expired during export. Please revise exported products and repeat the process if necessary."
"Synchronize","Synchronize"
"Target Country","Target Country"
"The item type has been saved.","The item type has been saved."
"This action will update items statistics and remove the items which are not available in Google Base. Continue?","This action will update items statistics and remove the items which are not available in Google Base. Continue?"
"Total of %d items(s) have been deleted; total of %d items(s) have been updated.","Total of %d items(s) have been deleted; total of %d items(s) have been updated."
"Total of %d items(s) have been published.","Total of %d items(s) have been published."
"Total of %d items(s) have been removed from Google Base.","Total of %d items(s) have been removed from Google Base."
"Total of %d items(s) have been saved as inactive items.","Total of %d items(s) have been saved as inactive items."
"Total of %d product(s) have been added to Google Base.","Total of %d product(s) have been added to Google Base."
"Unable to connect to Google Base. Please, check Account settings in configuration.","Unable to connect to Google Base. Please, check Account settings in configuration."
"Unable to select a Store View.","Unable to select a Store View."
"View Available Products","View Available Products"
"View Item in Google Base","View Item in Google Base"
"Yes","Yes"
