/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/******************************************/
/***********[ Mage_CSS_A Reset ]***********/
/******************************************/

* { margin:0; padding:0; }

body {
    background-color: #496778;
    color:#2f2f2f;
    font:12px/1.55em arial, helvetica, sans-serif;
    text-align:center;
    }

a { color:#1e7ec8; text-decoration:underline; }
a:hover { color:#1e7ec8; text-decoration:underline; }
a img { border:0;}

/* Heading */
h1, h2, h3, h4, h5, h6, .head {
    margin-bottom:.4em;
    line-height:1.3em;
    color:#0A263C;
    }
h2 { font-size:1.5em; }
h3 { font-size:1.35em; }
h4 { font-size:1.05em; }
h5 { font-size:1.05em; }
h6 { font-size:.95em; }

/* Table */
th { padding:0; text-align:left; vertical-align:top; }
td {padding:0;vertical-align:top;}

/* Paragraph */
p { margin-bottom:.8em; }
address { margin-bottom:.4em; }
address { font-style:normal; line-height:1.4em;}
cite { font-style:normal; font-size:10px;}
q:before, q:after{content:'';}

/* Form */
form { display:inline;}
fieldset { border:none; }
legend {display:none;}
label { color:#666; /*font-size:.95em;*/  font-weight:bold; }
input, select, button { vertical-align:middle; }

/* Lists */
dt { display:block; font-weight:bold; }
li { list-style:none; }

/* Size */
small { font-size:.9em; }
big { font-size:1.1em; }

hr { height:0; margin:8px 0; overflow:hidden; visibility:hidden; }
.nowrap { white-space:nowrap; }
:focus { outline: 0; }
.bold { font-weight:bold; }
