<?xml version="1.0" encoding="UTF-8"?>
<Struct>
		<Zend>
			<Log>
				<Formatter>
				</Formatter>
				<Filter>
				</Filter>
				<Writer>
				</Writer>
			</Log>
			<Mail>
				<Protocol>
					<Smtp>
						<Auth>
						</Auth>
					</Smtp>
				</Protocol>
				<Storage>
					<Writable>
					</Writable>
					<Folder>
					</Folder>
				</Storage>
				<Transport>
				</Transport>
			</Mail>
			<Session>
				<Validator>
				</Validator>
				<SaveHandler>
				</SaveHandler>
			</Session>
			<Filter>
			</Filter>
			<Mime>
			</Mime>
			<Request>
			</Request>
			<Service>
				<Yahoo>
				</Yahoo>
				<Delicious>
				</Delicious>
				<Amazon>
				</Amazon>
				<StrikeIron>
				</StrikeIron>
				<Simpy>
				</Simpy>
				<Flickr>
				</Flickr>
			</Service>
			<Measure>
				<Cooking>
				</Cooking>
				<Viscosity>
				</Viscosity>
				<Flow>
				</Flow>
				<fileName>Current.php</fileName>
			</Measure>
			<Console>
				<Getopt>
				</Getopt>
			</Console>
			<Feed>
				<Entry>
				</Entry>
				<Builder>
					<Header>
					</Header>
				</Builder>
			</Feed>
			<Config>
			</Config>
			<XmlRpc>
				<Server>
				</Server>
				<Request>
				</Request>
				<Value>
				</Value>
				<Client>
				</Client>
				<Response>
				</Response>
				<fileName>Value.php</fileName>
			</XmlRpc>
			<Pdf>
				<Cmap>
					<ByteEncoding>
					</ByteEncoding>
				</Cmap>
				<ElementFactory>
				</ElementFactory>
				<Filter>
					<Compression>
					</Compression>
				</Filter>
				<Resource>
					<Image>
					</Image>
					<Font>
						<Standard>
						</Standard>
						<OpenType>
						</OpenType>
					</Font>
				</Resource>
				<Element>
					<Object>
					</Object>
					<String>
					</String>
					<Reference>
					</Reference>
				</Element>
				<FileParser>
					<Image>
					</Image>
					<Font>
						<OpenType>
						</OpenType>
					</Font>
				</FileParser>
				<Parser>
				</Parser>
				<Trailer>
				</Trailer>
				<FileParserDataSource>
				</FileParserDataSource>
				<Color>
				</Color>
			</Pdf>
			<Http>
				<Client>
					<Adapter>
					</Adapter>
				</Client>
			</Http>
			<Gdata>
				<Media>
					<Extension>
					</Extension>
				</Media>
				<App>
					<Extension>
					</Extension>
				</App>
				<Gbase>
					<Extension>
					</Extension>
				</Gbase>
				<Kind>
				</Kind>
				<Spreadsheets>
					<Extension>
					</Extension>
				</Spreadsheets>
				<YouTube>
					<Extension>
					</Extension>
				</YouTube>
				<Calendar>
					<Extension>
					</Extension>
				</Calendar>
				<Gapps>
					<Extension>
					</Extension>
				</Gapps>
				<Extension>
				</Extension>
			</Gdata>
			<Controller>
				<Router>
					<Route>
					</Route>
				</Router>
				<Dispatcher>
				</Dispatcher>
				<Request>
				</Request>
				<Action>
					<Helper>
					</Helper>
				</Action>
				<Plugin>
				</Plugin>
				<Response>
				</Response>
			</Controller>
			<Db>
				<Select>
				</Select>
				<Adapter>
					<Pdo>
						<Ibm>
						</Ibm>
					</Pdo>
					<Oracle>
					</Oracle>
					<Db2>
					</Db2>
					<Mysqli>
					</Mysqli>
				</Adapter>
				<Profiler>
				</Profiler>
				<Statement>
					<Pdo>
					</Pdo>
					<Oracle>
					</Oracle>
					<Db2>
					</Db2>
					<Mysqli>
					</Mysqli>
				</Statement>
				<Table>
					<Rowset>
					</Rowset>
					<Row>
					</Row>
				</Table>
			</Db>
			<Validate>
				<Hostname>
				</Hostname>
			</Validate>
			<Currency>
			</Currency>
			<Translate>
				<Adapter>
				</Adapter>
			</Translate>
			<Locale>
				<Data>
				</Data>
				<Math>
				</Math>
			</Locale>
			<Acl>
				<Assert>
				</Assert>
				<Role>
					<Registry>
					</Registry>
				</Role>
				<Resource>
				</Resource>
			</Acl>
			<Uri>
			</Uri>
			<Auth>
				<Adapter>
					<Http>
						<Resolver>
						</Resolver>
					</Http>
				</Adapter>
				<Storage>
				</Storage>
			</Auth>
			<Cache>
				<Frontend>
				</Frontend>
				<Backend>
				</Backend>
			</Cache>
			<Search>
				<Lucene>
					<Analysis>
						<Analyzer>
							<Common>
								<Text>
								</Text>
								<TextNum>
								</TextNum>
							</Common>
						</Analyzer>
						<TokenFilter>
						</TokenFilter>
					</Analysis>
					<Storage>
						<File>
						</File>
						<Directory>
						</Directory>
					</Storage>
					<Search>
						<Query>
						</Query>
						<Similarity>
						</Similarity>
						<QueryEntry>
						</QueryEntry>
						<Weight>
						</Weight>
					</Search>
					<Index>
						<SegmentWriter>
						</SegmentWriter>
					</Index>
					<Document>
					</Document>
				</Lucene>
			</Search>
			<Server>
				<Reflection>
					<Function>
					</Function>
				</Reflection>
			</Server>
			<Memory>
				<Container>
				</Container>
				<fileName>Value.php</fileName>
			</Memory>
			<Json>
			</Json>
			<View>
				<Helper>
				</Helper>
				<Filter>
				</Filter>
			</View>
			<Date>
			</Date>
			<Rest>
				<Server>
				</Server>
				<Client>
				</Client>
			</Rest>
			<fileName>Mail.php</fileName>
		</Zend>
	</lib>
</Struct>
Array
(
    [tmp] => Array
        (
        )

    [Varien] => Array
        (
            [Http] => Array
                (
                    [Adapter] => Array
                        (
                        )

                )

            [File] => Array
                (
                    [Uploader] => Array
                        (
                        )

                )

            [Convert] => Array
                (
                    [Adapter] => Array
                        (
                            [Io] => Array
                                (
                                )

                        )

                    [Container] => Array
                        (
                            [Grid] => Array
                                (
                                )

                        )

                    [Mapper] => Array
                        (
                        )

                    [Validator] => Array
                        (
                        )

                )

            [Event] => Array
                (
                    [Observer] => Array
                        (
                        )

                )

            [Image] => Array
                (
                    [Adapter] => Array
                        (
                        )

                )

            [Controller] => Array
                (
                    [Router] => Array
                        (
                            [Route] => Array
                                (
                                )

                        )

                    [Dispatcher] => Array
                        (
                        )

                    [Plugin] => Array
                        (
                        )

                )

            [Filter] => Array
                (
                    [Object] => Array
                        (
                        )

                    [Template] => Array
                        (
                            [Tokenizer] => Array
                                (
                                )

                        )

                    [Array] => Array
                        (
                        )

                )

            [Db] => Array
                (
                    [Tree] => Array
                        (
                            [NodeSet] => Array
                                (
                                )

                            [Node] => Array
                                (
                                )

                        )

                    [Adapter] => Array
                        (
                            [Pdo] => Array
                                (
                                )

                        )

                )

            [Crypt] => Array
                (
                )

            [Io] => Array
                (
                )

            [Data] => Array
                (
                    [Tree] => Array
                        (
                            [Node] => Array
                                (
                                )

                        )

                    [Form] => Array
                        (
                            [Element] => Array
                                (
                                    [Renderer] => Array
                                        (
                                        )

                                )

                        )

                    [Collection] => Array
                        (
                        )

                )

            [Simplexml] => Array
                (
                    [Config] => Array
                        (
                            [Cache] => Array
                                (
                                )

                        )

                )

            [Directory] => Array
                (
                )

        )

    [PHPUnit] => Array
        (
            [Extensions] => Array
                (
                )

            [Tests] => Array
                (
                    [Extensions] => Array
                        (
                        )

                    [_files] => Array
                        (
                        )

                    [Runner] => Array
                        (
                        )

                    [Framework] => Array
                        (
                        )

                    [Util] => Array
                        (
                            [TestDox] => Array
                                (
                                )

                        )

                )

            [TextUI] => Array
                (
                )

            [Samples] => Array
                (
                    [Money] => Array
                        (
                        )

                    [BankAccount] => Array
                        (
                        )

                )

            [Runner] => Array
                (
                )

            [Framework] => Array
                (
                    [MockObject] => Array
                        (
                            [Matcher] => Array
                                (
                                )

                            [Builder] => Array
                                (
                                )

                            [Stub] => Array
                                (
                                )

                        )

                    [ComparisonFailure] => Array
                        (
                        )

                    [Constraint] => Array
                        (
                        )

                )

            [Util] => Array
                (
                    [Log] => Array
                        (
                        )

                    [Skeleton] => Array
                        (
                        )

                    [TestDox] => Array
                        (
                            [ResultPrinter] => Array
                                (
                                )

                        )

                    [Database] => Array
                        (
                        )

                    [Report] => Array
                        (
                            [Test] => Array
                                (
                                    [Node] => Array
                                        (
                                        )

                                )

                            [Coverage] => Array
                                (
                                    [Node] => Array
                                        (
                                        )

                                )

                            [Template] => Array
                                (
                                )

                        )

                )

        )

    [Zend] => Array
        (
            [Log] => Array
                (
                    [Formatter] => Array
                        (
                        )

                    [Filter] => Array
                        (
                        )

                    [Writer] => Array
                        (
                        )

                )

            [Mail] => Array
                (
                    [Protocol] => Array
                        (
                            [Smtp] => Array
                                (
                                    [Auth] => Array
                                        (
                                        )

                                )

                        )

                    [Storage] => Array
                        (
                            [Writable] => Array
                                (
                                )

                            [Folder] => Array
                                (
                                )

                        )

                    [Transport] => Array
                        (
                        )

                )

            [Session] => Array
                (
                    [Validator] => Array
                        (
                        )

                    [SaveHandler] => Array
                        (
                        )

                )

            [Filter] => Array
                (
                )

            [Mime] => Array
                (
                )

            [Request] => Array
                (
                )

            [Service] => Array
                (
                    [Yahoo] => Array
                        (
                        )

                    [Delicious] => Array
                        (
                        )

                    [Amazon] => Array
                        (
                        )

                    [StrikeIron] => Array
                        (
                        )

                    [Simpy] => Array
                        (
                        )

                    [Flickr] => Array
                        (
                        )

                )

            [Measure] => Array
                (
                    [Cooking] => Array
                        (
                        )

                    [Viscosity] => Array
                        (
                        )

                    [Flow] => Array
                        (
                        )

                    [files_in_dirs] => Array
                        (
                            [0] => Current.php
                        )

                )

            [Console] => Array
                (
                    [Getopt] => Array
                        (
                        )

                )

            [Feed] => Array
                (
                    [Entry] => Array
                        (
                        )

                    [Builder] => Array
                        (
                            [Header] => Array
                                (
                                )

                        )

                )

            [Config] => Array
                (
                )

            [XmlRpc] => Array
                (
                    [Server] => Array
                        (
                        )

                    [Request] => Array
                        (
                        )

                    [Value] => Array
                        (
                        )

                    [Client] => Array
                        (
                        )

                    [Response] => Array
                        (
                        )

                    [files_in_dirs] => Array
                        (
                            [0] => Value.php
                        )

                )

            [Pdf] => Array
                (
                    [Cmap] => Array
                        (
                            [ByteEncoding] => Array
                                (
                                )

                        )

                    [ElementFactory] => Array
                        (
                        )

                    [Filter] => Array
                        (
                            [Compression] => Array
                                (
                                )

                        )

                    [Resource] => Array
                        (
                            [Image] => Array
                                (
                                )

                            [Font] => Array
                                (
                                    [Standard] => Array
                                        (
                                        )

                                    [OpenType] => Array
                                        (
                                        )

                                )

                        )

                    [Element] => Array
                        (
                            [Object] => Array
                                (
                                )

                            [String] => Array
                                (
                                )

                            [Reference] => Array
                                (
                                )

                        )

                    [FileParser] => Array
                        (
                            [Image] => Array
                                (
                                )

                            [Font] => Array
                                (
                                    [OpenType] => Array
                                        (
                                        )

                                )

                        )

                    [Parser] => Array
                        (
                        )

                    [Trailer] => Array
                        (
                        )

                    [FileParserDataSource] => Array
                        (
                        )

                    [Color] => Array
                        (
                        )

                )

            [Http] => Array
                (
                    [Client] => Array
                        (
                            [Adapter] => Array
                                (
                                )

                        )

                )

            [Gdata] => Array
                (
                    [Media] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [App] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [Gbase] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [Kind] => Array
                        (
                        )

                    [Spreadsheets] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [YouTube] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [Calendar] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [Gapps] => Array
                        (
                            [Extension] => Array
                                (
                                )

                        )

                    [Extension] => Array
                        (
                        )

                )

            [Controller] => Array
                (
                    [Router] => Array
                        (
                            [Route] => Array
                                (
                                )

                        )

                    [Dispatcher] => Array
                        (
                        )

                    [Request] => Array
                        (
                        )

                    [Action] => Array
                        (
                            [Helper] => Array
                                (
                                )

                        )

                    [Plugin] => Array
                        (
                        )

                    [Response] => Array
                        (
                        )

                )

            [Db] => Array
                (
                    [Select] => Array
                        (
                        )

                    [Adapter] => Array
                        (
                            [Pdo] => Array
                                (
                                    [Ibm] => Array
                                        (
                                        )

                                )

                            [Oracle] => Array
                                (
                                )

                            [Db2] => Array
                                (
                                )

                            [Mysqli] => Array
                                (
                                )

                        )

                    [Profiler] => Array
                        (
                        )

                    [Statement] => Array
                        (
                            [Pdo] => Array
                                (
                                )

                            [Oracle] => Array
                                (
                                )

                            [Db2] => Array
                                (
                                )

                            [Mysqli] => Array
                                (
                                )

                        )

                    [Table] => Array
                        (
                            [Rowset] => Array
                                (
                                )

                            [Row] => Array
                                (
                                )

                        )

                )

            [Validate] => Array
                (
                    [Hostname] => Array
                        (
                        )

                )

            [Currency] => Array
                (
                )

            [Translate] => Array
                (
                    [Adapter] => Array
                        (
                        )

                )

            [Locale] => Array
                (
                    [Data] => Array
                        (
                        )

                    [Math] => Array
                        (
                        )

                )

            [Acl] => Array
                (
                    [Assert] => Array
                        (
                        )

                    [Role] => Array
                        (
                            [Registry] => Array
                                (
                                )

                        )

                    [Resource] => Array
                        (
                        )

                )

            [Uri] => Array
                (
                )

            [Auth] => Array
                (
                    [Adapter] => Array
                        (
                            [Http] => Array
                                (
                                    [Resolver] => Array
                                        (
                                        )

                                )

                        )

                    [Storage] => Array
                        (
                        )

                )

            [Cache] => Array
                (
                    [Frontend] => Array
                        (
                        )

                    [Backend] => Array
                        (
                        )

                )

            [Search] => Array
                (
                    [Lucene] => Array
                        (
                            [Analysis] => Array
                                (
                                    [Analyzer] => Array
                                        (
                                            [Common] => Array
                                                (
                                                    [Text] => Array
                                                        (
                                                        )

                                                    [TextNum] => Array
                                                        (
                                                        )

                                                )

                                        )

                                    [TokenFilter] => Array
                                        (
                                        )

                                )

                            [Storage] => Array
                                (
                                    [File] => Array
                                        (
                                        )

                                    [Directory] => Array
                                        (
                                        )

                                )

                            [Search] => Array
                                (
                                    [Query] => Array
                                        (
                                        )

                                    [Similarity] => Array
                                        (
                                        )

                                    [QueryEntry] => Array
                                        (
                                        )

                                    [Weight] => Array
                                        (
                                        )

                                )

                            [Index] => Array
                                (
                                    [SegmentWriter] => Array
                                        (
                                        )

                                )

                            [Document] => Array
                                (
                                )

                        )

                )

            [Server] => Array
                (
                    [Reflection] => Array
                        (
                            [Function] => Array
                                (
                                )

                        )

                )

            [Memory] => Array
                (
                    [Container] => Array
                        (
                        )

                    [files_in_dirs] => Array
                        (
                            [0] => Value.php
                        )

                )

            [Json] => Array
                (
                )

            [View] => Array
                (
                    [Helper] => Array
                        (
                        )

                    [Filter] => Array
                        (
                        )

                )

            [Date] => Array
                (
                )

            [Rest] => Array
                (
                    [Server] => Array
                        (
                        )

                    [Client] => Array
                        (
                        )

                )

            [files_in_dirs] => Array
                (
                    [0] => Mail.php
                )

        )

)

4254
<?php
13730
<?php
4056
<?php
16349
<?php
