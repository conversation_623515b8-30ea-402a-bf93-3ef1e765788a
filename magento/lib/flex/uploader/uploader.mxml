<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Uploader
 * @copyright  Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
-->
<mx:Application
	xmlns:mx="http://www.adobe.com/2006/mxml"
	width="230"
	height="20"
	xmlns:upload="varien.upload.*"
	xmlns:bridge="bridge.*"
	layout="absolute"
	backgroundGradientAlphas="[1.0, 1.0]"
  	backgroundGradientColors="[#D7E5EF, #D7E5EF]"
	>
	<upload:Uploader id="upload" ></upload:Uploader>
	<bridge:FABridge></bridge:FABridge>
	<mx:Button
		label="Browse Files..."
		click="upload.browse();"
		alpha="1"
		width="110"
		height="20"
		borderColor="#ED6502"
		color="#FFFFFF"
		textRollOverColor="#FFFFFF"
		fontSize="12"
		fontFamily="arial,helvetica,sans-serif"
		cornerRadius="0"
		x="0" 
		y="0"
		fillAlphas="[1.0, 1.0]"
		fillColors="[#FA8F29, #FA8F29]"
		>
	</mx:Button>
	<mx:Button
		label="Upload Files"
		click="upload.upload();"
		alpha="1"
		width="110"
		height="20"
		borderColor="#ED6502"
		color="#FFFFFF"
		textRollOverColor="#FFFFFF"
		fontSize="12"
		fontFamily="arial,helvetica,sans-serif"
		cornerRadius="0"
		x="120" 
		y="0"
		fillAlphas="[1.0, 1.0]"
		fillColors="[#FA8F29, #FA8F29]"
		>
	</mx:Button>
</mx:Application>