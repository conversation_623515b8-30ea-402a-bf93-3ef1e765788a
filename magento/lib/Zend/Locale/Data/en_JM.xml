<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9280 $"/>
		<generation date="$Date: 2013-08-27 13:07:13 -0500 (Tue, 27 Aug 2013) $"/>
		<language type="en"/>
		<territory type="JM"/>
	</identity>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">d/M/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="Md" draft="unconfirmed">d/M</dateFormatItem>
						<dateFormatItem id="MEd" draft="unconfirmed">E, d/M</dateFormatItem>
						<dateFormatItem id="yyyyMEd" draft="unconfirmed">E, d/M/y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="unconfirmed">d/M - d/M</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d/M - d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="unconfirmed">E, d/M - E, d/M</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E, d/M - E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="unconfirmed">d/M/y - d/M/y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d/M/y - d/M/y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">d/M/y - d/M/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="unconfirmed">E, d/M/y - E, d/M/y G</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E, d/M/y - E, d/M/y G</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E, d/M/y - E, d/M/y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">d/M/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<availableFormats>
						<dateFormatItem id="Md" draft="unconfirmed">d/M</dateFormatItem>
						<dateFormatItem id="MEd" draft="unconfirmed">E, d/M</dateFormatItem>
						<dateFormatItem id="yMEd" draft="unconfirmed">E, d/M/y</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatItem id="Md">
							<greatestDifference id="d" draft="unconfirmed">d/M - d/M</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d/M - d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d" draft="unconfirmed">E, d/M - E, d/M</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E, d/M - E, d/M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d" draft="unconfirmed">d/M/y - d/M/y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">d/M/y - d/M/y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">d/M/y - d/M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d" draft="unconfirmed">E, d/M/y - E, d/M/y</greatestDifference>
							<greatestDifference id="M" draft="unconfirmed">E, d/M/y - E, d/M/y</greatestDifference>
							<greatestDifference id="y" draft="unconfirmed">E, d/M/y - E, d/M/y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
		</calendars>
	</dates>
	<numbers>
		<currencies>
			<currency type="JMD">
				<symbol>$</symbol>
			</currency>
		</currencies>
	</numbers>
</ldml>

