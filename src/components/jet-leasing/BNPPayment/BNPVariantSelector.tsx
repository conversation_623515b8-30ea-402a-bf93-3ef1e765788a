'use client'
import React from 'react'
import { BNPVariantSelectorProps, BNPPricingVariant } from './types'
import { formatPrice, formatPercentage } from './utils'

export const BNPVariantSelector: React.FC<BNPVariantSelectorProps> = ({
  variantGroups,
  selectedVariant,
  selectedScheme,
  onVariantSelectAction,
  loading = true,
  className = '',
}) => {

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!variantGroups.length) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-800">
            За да използвате лизинг чрез BNP Paribas, сумата на продукта трябва да бъде най-малко 150 лева или общата
            сума на артикулите в количката ви трябва да бъде най-малко 150 лева.
          </p>
        </div>
      </div>
    )
  }

  const renderVariantTable = (variants: BNPPricingVariant[]) => {
    // Sort variants - put promo variants (0% APR) first
    const sortedVariants = [...variants].sort((a, b) => {
      const aIsPromo = parseFloat(a.apr) === 0
      const bIsPromo = parseFloat(b.apr) === 0
      if (aIsPromo && !bIsPromo) return -1
      if (!aIsPromo && bIsPromo) return 1
      return parseInt(a.maturity) - parseInt(b.maturity)
    })

    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse bg-white rounded-lg shadow-sm">
          <thead>
            <tr className="bg-gray-50">
              <th className="text-left p-3 border-b border-gray-200 font-semibold text-gray-700"></th>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                const maturity = isPromo ? `${variant.maturity}+1` : variant.maturity
                return (
                  <th
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 font-semibold ${
                      isPromo ? 'bg-green-50 text-green-800' : 'text-gray-700'
                    }`}
                  >
                    <div className="flex flex-col items-center">
                      {isPromo && (
                        <span className="bg-green-500 text-white text-xs px-2 py-1 rounded-full mb-1">ПРОМО</span>
                      )}
                      <span>{maturity} месеца</span>
                    </div>
                  </th>
                )
              })}
            </tr>
          </thead>
          <tbody>
            {/* Installment Amount Row */}
            <tr className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 font-medium text-gray-700">Месечна вноска</td>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 ${isPromo ? 'bg-green-50' : ''}`}
                  >
                    <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                      <span className="font-semibold text-orange-600">{formatPrice(variant.installmentAmount)}</span>
                    </label>
                  </td>
                )
              })}
            </tr>

            {/* APR Row */}
            <tr className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 font-medium text-gray-700">ГПР</td>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 ${isPromo ? 'bg-green-50' : ''}`}
                  >
                    <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                      <span className="font-semibold">{formatPercentage(variant.apr)}</span>
                    </label>
                  </td>
                )
              })}
            </tr>

            {/* NIR Row */}
            <tr className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 font-medium text-gray-700">НЛР</td>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 ${isPromo ? 'bg-green-50' : ''}`}
                  >
                    <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                      <span className="font-semibold">{formatPercentage(variant.nir)}</span>
                    </label>
                  </td>
                )
              })}
            </tr>

            {/* Processing Fee Row */}
            <tr className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 font-medium text-gray-700">Такса обработка</td>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 ${isPromo ? 'bg-green-50' : ''}`}
                  >
                    <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                      <span className="font-semibold">{formatPrice(variant.processingFeeAmount || '50.00')}</span>
                    </label>
                  </td>
                )
              })}
            </tr>

            {/* Total Repayment Row */}
            <tr className="hover:bg-gray-50">
              <td className="p-3 border-b border-gray-200 font-medium text-gray-700">Обща сума за възстановяване</td>
              {sortedVariants.map((variant) => {
                const isPromo = parseFloat(variant.apr) === 0
                return (
                  <td
                    key={variant.id}
                    className={`text-center p-3 border-b border-gray-200 ${isPromo ? 'bg-green-50' : ''}`}
                  >
                    <label htmlFor={`variant_${variant.id}`} className="cursor-pointer block">
                      <span className="font-semibold">{formatPrice(variant.totalRepaymentAmount)}</span>
                    </label>
                  </td>
                )
              })}
            </tr>
          </tbody>

          {/* Radio Button Footer */}
          <tfoot>
            <tr>
              <td className="p-3 font-medium text-gray-700">
                <span className="text-red-500 text-sm">Моля, изберете една от опциите.</span>
              </td>
              {sortedVariants.map((variant) => (
                <td key={variant.id} className="text-center p-3">
                  <input
                    type="radio"
                    id={`variant_${variant.id}`}
                    name="bnp_variant"
                    value={variant.id}
                    checked={selectedVariant?.id === variant.id}
                    onChange={() => onVariantSelectAction(variant)}
                    className="w-4 h-4 text-orange-600 border-gray-300 focus:ring-orange-500"
                  />
                </td>
              ))}
            </tr>
          </tfoot>
        </table>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Tabs */}
      {variantGroups.length > 0 && (
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {variantGroups.map((group, index) => (
              <button
                type="button"
                key={group.schemeId}
                onClick={() => setActiveTab(index)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === index
                    ? 'border-orange-500 text-orange-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {group.schemeName}
              </button>
            ))}
          </nav>
        </div>
      )}
      {/* Active Tab Content */}
      {variantGroups[activeTab] && renderVariantTable(variantGroups[activeTab].variants)}
    </div>
  )
}
