import React from 'react'
import { BNPPaymentStep } from '@components/jet-leasing/BNPPayment'

export const <PERSON><PERSON><PERSON><PERSON>izard: React.FC<{ activeStep: BNPPaymentStep }> = ({ activeStep }) => {
  const steps = [
    // { key: 'schemes', label: 'Схеми', completed: activeStep !== 'schemes' },
    { key: 'variants', label: 'Варианти', completed: ['variants', 'customer', 'confirmation'].includes(activeStep) },
    { key: 'customer', label: 'Данни', completed: ['customer', 'confirmation'].includes(activeStep) },
    { key: 'confirmation', label: 'Потвърждение', completed: activeStep === 'confirmation' },
  ]

  return (
    <div className="flex items-center justify-center mb-8">
      {steps.map((step, index) => (
        <React.Fragment key={step.key}>
          <div className="flex items-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                activeStep === step.key
                  ? 'bg-orange-600 text-white'
                  : step.completed
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-300 text-gray-600'
              }`}
            >
              {step.completed && activeStep !== step.key ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                index + 1
              )}
            </div>
            <span
              className={`ml-2 text-sm font-medium ${activeStep === step.key ? 'text-orange-600' : 'text-gray-500'}`}
            >
              {step.label}
            </span>
          </div>
          {index < steps.length - 1 && (
            <div className={`w-12 h-0.5 mx-4 ${steps[index + 1].completed ? 'bg-green-600' : 'bg-gray-300'}`} />
          )}
        </React.Fragment>
      ))}
    </div>
  )
}
