'use client'
import React, { useState, useEffect, useCallback } from 'react'
import {
  BNPPaymentProps,
  BNPPaymentState,
  BNPPricingScheme,
  BNPPricingVariant,
  BNPCustomerData,
  BNPPaymentInput,
} from './types'
import { handleBNPGoodTypeError } from './utils'
import { GraphQLBackend } from '@lib/api/graphql'
import { ParibaWizard } from '@components/jet-leasing/BNPPayment/components/ParibaWizard'
import { ParibaHeader } from '@components/jet-leasing/BNPPayment/components/ParibaHeader'
import { ParibaError } from '@components/jet-leasing/BNPPayment/components/ParibaError'
import { ParibaStepSchemes } from '@components/jet-leasing/BNPPayment/components/ParibaStepSchemes'
import { ParibaStepVariants } from './components/ParibaStepVariants'
import { BNPDownPayment } from '@components/jet-leasing/BNPPayment/BNPDownPayment'
import { BNPCustomerForm } from '@components/jet-leasing/BNPPayment/BNPCustomerForm'

export const BNPPayment: React.FC<BNPPaymentProps> = ({
  cartToken,
  totalPrice,
  onPaymentSelect,
  onError,
  className = '',
}) => {
  const [state, setState] = useState<BNPPaymentState>({
    step: 'variants',
    loading: false,
    error: null,
    schemes: [],
    selectedScheme: null,
    variantGroups: [],
    selectedVariant: null,
    loanCalculation: null,
    customerData: null,
    downPayment: 0,
    goodTypeIds: '353', // Static fallback good type ID
    privacyAgreed: false,
  })
  const { goodTypeIds, downPayment, step } = state

  const updateState = (updates: Partial<BNPPaymentState>) => {
    setState((prev) => ({ ...prev, ...updates }))
  }

  const handleVariantSelect = async (variant: BNPPricingVariant) => {
    updateState({ selectedVariant: variant })
  }

  const handleDownPaymentChange = (newDownPayment: number) => {
    updateState({ loading: true })
    setTimeout(() => {
      loadPricingSchemes().finally(() => {
        updateState({ loading: false })
        updateState({ downPayment: newDownPayment })
      })
    }, 2000)
  }

  const handleSchemeSelect = (schemeId: BNPPricingScheme['id']) => {
    updateState({ step: 'variants', selectedScheme: schemeId, error: null })
  }

  const loadPricingSchemes = useCallback(async () => {
    updateState({ loading: true, error: null })
    try {
      try {
        const response = await GraphQLBackend.GetBNPPricingSchemes({
          goodTypeIds: goodTypeIds,
          principal: totalPrice,
          downPayment: downPayment,
        })
        updateState({
          schemes: response.getBNPPricingSchemes,
          loading: false,
        })
      } catch (firstError: any) {
        updateState({
          schemes: [],
          loading: false,
          error: 'Няма налични схеми за плащане за този продукт',
        })
      }
    } catch (err) {
      const errorMessage = handleBNPGoodTypeError(err)
      updateState({
        error: errorMessage,
        loading: false,
        schemes: [],
      })
    }
  }, [downPayment, goodTypeIds, totalPrice])

  const handleCustomerFormSubmit = async (customerData: BNPCustomerData) => {
    if (!state.selectedVariant || !state.loanCalculation) {
      onError?.('Моля, изберете схема на плащане')
      return
    }
    console.log('HERE 2')

    updateState({ loading: true, customerData })

    try {
      const paymentData: BNPPaymentInput = {
        downPayment: state.downPayment,
        pricingVariantId: parseInt(state.selectedVariant.id),
        customerData,
      }

      // Save BNP payment data to cart using GraphQL mutation
      const response = await GraphQLBackend.CartSaveBNPPayment({
        cartToken,
        paymentData,
      })

      if (response.cartSaveBNPPayment) {
        updateState({ step: 'confirmation' })
        onPaymentSelect?.(paymentData)
      } else {
        throw new Error('Неуспешно запазване на данните за плащане')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Грешка при запазване на данните'
      updateState({ error: errorMessage })
      onError?.(errorMessage)

      // Log detailed error for debugging
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ BNP CartSaveBNPPayment error:', {
          error: err,
          customerData,
          selectedVariant: state.selectedVariant,
        })
      }
    } finally {
      updateState({ loading: false })
    }
  }

  const handleBackToVariants = () => {
    updateState({ step: 'variants', error: null })
  }

  const handleBackToSchemes = () => {
    updateState({ step: 'schemes', error: null })
  }

  // Load pricing schemes when component mounts or when dependencies change
  useEffect(() => {
    if (step === 'schemes') {
      loadPricingSchemes()
    }
  }, [loadPricingSchemes, step])

  const handleProceedToCustomerForm = () => {
    if (!state.selectedVariant) {
      updateState({ error: 'Моля, изберете схема на плащане' })
      return
    }
    updateState({ step: 'customer', error: null })
  }

  return (
    <div className={`max-w-4xl mx-auto ${className}`}>
      {/* BNP Paribas Logo/Header */}
      <ParibaHeader />

      {/* Step Indicator */}
      <ParibaWizard activeStep={state.step} />

      {/* Error Display */}
      {state.error && <ParibaError error={state.error} />}

      <div className="space-y-6">
        <h3 className="text-lg font-semibold text-gray-900 text-center">Изчислете вашите вноски</h3>

        <BNPDownPayment
          downPayment={state.downPayment}
          onDownPaymentChangeAction={handleDownPaymentChange}
          totalPrice={totalPrice}
        />

        {state.step === 'schemes' && (
          <ParibaStepSchemes schemes={state.schemes} loading={state.loading} onSelectScheme={handleSchemeSelect} />
        )}

        {state.step === 'variants' && (
          <ParibaStepVariants
            downPayment={state.downPayment}
            loading={state.loading}
            onLoanCalculation={(loanCalculation) => updateState({ loanCalculation })}
            onSelectVariant={handleVariantSelect}
            selectedScheme={state.selectedScheme}
            onBack={handleBackToSchemes}
            onNext={handleProceedToCustomerForm}
          />
        )}

        {state.step === 'customer' && (
          <BNPCustomerForm onSubmit={handleCustomerFormSubmit} onBack={handleBackToVariants} loading={state.loading} />
        )}
        {/*{state.step === 'confirmation' && (*/}
        {/*  <ParibaStepConfirmation selectedVariant={state.selectedVariant} customerData={state.customerData} />*/}
        {/*)}*/}
      </div>
    </div>
  )
}
