import { ApiKeys, PageMessages, StaticContent } from '@lib/_generated/graphql_sdk'

export const staticSelectors = {
  // API Keys
  selectGoogleMapsApiKey: (content: StaticContent | null) => content?.apiKeys.googleMaps,

  // Header
  selectHeaderScripts: (content: StaticContent | null) => content?.header.scripts,

  // Messages
  // selectPrivacyMessage: (content: StaticContent | null) => content?.messages.privacy,
  selectNewsletterMessage: (content: StaticContent | null) => content?.messages.newsletter,

  // Footer
  selectFooter: (content: StaticContent | null) => content?.footer,

  // Menu
  selectMenuCategories: (content: StaticContent | null) => content?.menu.categories,

  selectStoreContacts: (content: StaticContent | null) => content?.store.contacts,

  // Store Base URL
  selectStoreBaseUrl: (content: StaticContent | null) => content?.store.baseUrl,

  // Store Location
  selectStoreLocation: (content: StaticContent | null) => content?.store.location,
  selectStoreLatitude: (content: StaticContent | null) => content?.store.location.lat,
  selectStoreLongitude: (content: StaticContent | null) => content?.store.location.lng,

  // Typename
  selectTypename: (content: StaticContent | null) => content?.__typename,

  // GDPR
  selectGDPR: (content: StaticContent | null) => content?.gdpr,

  // Messages
  selectMessages: (content: StaticContent | null): PageMessages =>
    content?.messages || {
      newsletter: '',
      sendInquiryMessage: '',
    },

  // Select API keys
  selectApiKeys: (content: StaticContent | null): ApiKeys =>
    content?.apiKeys || {
      facebookLoginAppId: '',
      googleLoginClientId: '',
      googleMaps: '',
      googleRecaptchaKey: '',
    },
}
