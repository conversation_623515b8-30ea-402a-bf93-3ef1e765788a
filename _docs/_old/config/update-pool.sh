#!/bin/bash

# Function to check if the input is a valid integer within the specified range
is_valid_integer() {
    local value=$1
    [[ "$value" =~ ^[0-9]+$ ]] && (( value > 10 && value < 500 ))
}

# Main script starts here
# Path to the PHP-FPM configuration file
fpm_conf_path="/etc/php/7.4/fpm/pool.d/www.conf" # Update this path as per your system

# Check if the script received an argument
if [ $# -eq 0 ]; then
    echo "Usage: $0 <integer>"
    exit 1
fi

# Validate the input
if ! is_valid_integer "$1"; then
    echo "Error: The provided argument must be an integer between 10 and 500."
    exit 1
fi

# Update the configuration file
sed -i "s/pm.max_children = [0-9]*/pm.max_children = $1/" "$fpm_conf_path"

# Restart PHP-FPM service
sudo service php7.4-fpm restart

echo "PHP-FPM configuration updated and service restarted."