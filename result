[GIN] 2025/06/21 - 14:07:55 | 204 |      19.081µs |       ********* | OPTIONS  "/graphql"
BNP: === PAYMENT DATA VALIDATION START ===
BNP: Validating BNP payment data...
BNP: Validating payment parameters...
BNP: Payment parameters valid - DownPayment: 0.00, VariantID: 481435
BNP: Validating customer data...
BNP: Customer data valid - Name: <PERSON><PERSON><PERSON>anasov, Email: <EMAIL>, Phone: 0883555204
BNP: === PAYMENT DATA VALIDATION COMPLETED ===
BNP: Status: SUCCESS - All validation checks passed

ackages/magento-core/mage-store/sales/quote-repository.go:186
[0.178ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144951

ackages/magento-core/mage-store/sales/quote-repository.go:186
[0.678ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = 'ed9501d2-22b6-49fd-9ade-b2a9e827195d' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1
BNP: === PAYMENT DATA UPDATE START ===
BNP: Quote ID: 1144951
BNP: Variant ID: 481435, Down Payment: 0.00
BNP: Updating payment method to BNP Paribas...
UpdateQuotePaymentMethod: Processing payment ID 1838697, current method: stenik_leasingjetcredit, target method: stenik_leasingjetcredit
UpdateQuotePaymentMethod: Payment method already correct for payment ID 1838697, skipping update
BNP: Payment method updated successfully
BNP: Calculated principal from cart subtotal: 198.00
BNP: === DETERMINING GOOD TYPE IDS FROM CART ===

BNP: Analyzing 1 cart items for good type IDs
ackages/magento-core/mage-store/sales/quote-item.go:67
[0.642ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144951
BNP: Processing cart item 1: SKU=3404040, Qty=1.00, Price=198.00
BNP: Using good type IDs '353' from product 3404040 (item 1)
BNP: Successfully determined good type IDs: 353
BNP: Determined good type IDs: 353
BNP: Calculating loan details...
BNP: Loan parameters - GoodTypeIds: 353, Principal: 198.00, DownPayment: 0.00, VariantID: 481435
BNP: Starting BNP Calculator API initialization
BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
BNP: Using merchant ID: 433147
BNP: Using merchant ID: 433147
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Operating in mode: PRODUCTION
BNP: Starting TLS configuration setup
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: No key password configured (as expected)
BNP: Loading X509 certificate/key pair
BNP: Successfully loaded X509 certificate/key pair
BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
BNP: Certificate count in TLS config: 1
BNP: TLS configuration loaded successfully
BNP: === BUSINESS LOGIC ===
BNP: Operation: Parameter Validation
BNP: Timestamp: 2025-06-21T14:07:55Z
BNP: method: CalculateLoan
BNP: goodTypeIds: 353
BNP: principal: 198
BNP: downPayment: 0
BNP: pricingVariantId: 481435
BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: CalculateLoan
BNP: URL Parameters: [433147 353 198.00 0.00 481435]
BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/CalculateLoan/433147/353/198.00/0.00/481435
BNP: === OUTGOING REQUEST ===
BNP: Method: CalculateLoan
BNP: Timestamp: 2025-06-21T14:07:55Z
BNP: URL: https://ws.pbpf.bg/ServicesPricing/CalculateLoan/433147/353/198.00/0.00/481435
BNP: HTTP Method: GET
BNP: Request Headers:
BNP:   User-Agent: MerchantPos
BNP: Request Parameters:
BNP:   Service Path: ServicesPricing
BNP:   API Method: CalculateLoan
BNP:   Merchant ID: 433147
BNP:   Good Type IDs: 353
BNP:   Principal: 198.00
BNP:   Down Payment: 0.00
BNP:   Additional Param[6]: 481435
BNP: Full Request Dump:
GET /ServicesPricing/CalculateLoan/433147/353/198.00/0.00/481435 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

BNP: === INCOMING RESPONSE ===
BNP: Method: CalculateLoan
BNP: Timestamp: 2025-06-21T14:07:55Z
BNP: Duration: 198.227207ms
BNP: Status Code: 200
BNP: Status: 200 OK
BNP: Response Headers:
BNP:   Cache-Control: private
BNP:   Content-Type: application/xml; charset=utf-8
BNP:   Server: Microsoft-IIS/10.0
BNP:   X-Aspnet-Version: 4.0.30319
BNP:   X-Powered-By: ASP.NET
BNP:   Date: Sat, 21 Jun 2025 14:07:55 GMT
BNP:   Content-Length: 583
BNP: Response Body Length: 583 bytes
BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><CreditProposition><APR>23.76</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>68.38</InstallmentAmount><Maturity>3</Maturity><NIR>21.47</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481435</PricingVariantId><ProcessingFeeAmount>0</ProcessingFeeAmount><TotalRepaymentAmount>205.13</TotalRepaymentAmount></CreditProposition></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
BNP: Response Status: SUCCESS
BNP: === BUSINESS LOGIC ===
BNP: Operation: Response Validation
BNP: Timestamp: 2025-06-21T14:07:55Z
BNP: errorCode: 0
BNP: errorMessage:
BNP: hasLoanData: true
BNP: === BUSINESS LOGIC ===
BNP: Operation: Data Transformation
BNP: Timestamp: 2025-06-21T14:07:55Z
BNP: processingFeeAmount: 0
BNP: success: true
BNP: apr: 23.76
BNP: installmentAmount: 68.38
BNP: maturity: 3
BNP: totalRepaymentAmount: 205.13
BNP: CalculateLoan completed successfully in 198.227207ms for variant 481435
BNP: Loan Details - APR: 23.76, Installment: 68.38, Maturity: 3 months, Total: 205.13
BNP: Loan calculation successful - APR: 23.76, Installment: 68.38, Total: 205.13
BNP: Preparing payment additional information...
BNP: Customer data - Name: Stoyan Atanasov, Email: <EMAIL>, Phone: 0883555204
BNP: Serializing payment data to PHP format for Magento compatibility...
BNP: Payment data serialized successfully (990 bytes)
BNP: Updating quote payment records...
BNP: Using existing quote payment record
BNP: Updating payment record 1 (ID: 1838697)
BNP: Payment record 1838697 updated successfully
BNP: Storing persistent payment data for quote 1144951 (710 bytes)

nternal/praktis/checkout/cart-utils/payments.go:328
[6.908ms] [rows:1] UPDATE `sales_flat_quote_payment` SET `additional_information`='a:10:{s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:5:"phone";s:10:"0883555204";s:13:"customer_data";a:11:{s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";s:9:"post_code";s:4:"1000";s:12:"company_name";s:0:"";s:3:"mol";s:0:"";s:3:"eik";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. teres";s:4:"city";s:5:"sofia";s:3:"egn";s:10:"9102288468";}s:10:"variant_id";i:481435;s:5:"names";s:15:"Stoyan Atanasov";s:13:"good_type_ids";s:3:"353";s:9:"principal";s:3:"198";s:4:"loan";a:10:{s:22:"total_repayment_amount";s:6:"205.13";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"68.38";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:21:"processing_fee_amount";s:1:"0";s:3:"apr";s:5:"23.76";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481435";}s:11:"downpayment";s:1:"0";}' WHERE `payment_id` = 1838697

nternal/praktis/checkout/cart-utils/payments.go:374
[1.628ms] [rows:0]
		CREATE TABLE IF NOT EXISTS bnp_payment_data_storage (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			quote_id bigint(20) NOT NULL,
			payment_data text NOT NULL,
			created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY unique_quote_id (quote_id),
			KEY idx_quote_id (quote_id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci


nternal/praktis/checkout/cart-utils/payments.go:402
[1.650ms] [rows:1]
		INSERT INTO bnp_payment_data_storage (quote_id, payment_data, created_at, updated_at)
		VALUES (1144951, '{"customer_data":{"address":"zh.k. teres","city":"sofia","company_name":"","egn":"9102288468","eik":"","email":"<EMAIL>","first_name":"Stoyan","last_name":"Atanasov","mol":"","phone":"0883555204","post_code":"1000"},"downpayment":0,"email":"<EMAIL>","good_type_ids":"353","loan":{"apr":"23.76","correct_downpayment_amount":"0","installment_amount":"68.38","maturity":"3","nir":"21.47","pricing_scheme_id":"1695","pricing_scheme_name":"1,2% месечно оскъпяване","pricing_variant_id":"481435","processing_fee_amount":"0","total_repayment_amount":"205.13"},"names":"Stoyan Atanasov","phone":"0883555204","pin":"9102288468","principal":198,"variant_id":481435}', NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		payment_data = VALUES(payment_data),
		updated_at = NOW()

BNP: Persistent payment data stored successfully in dedicated table
BNP: === PAYMENT DATA UPDATE COMPLETED ===
BNP: Quote: 1144951, Updated 1 payment records, Status: SUCCESS
BNP: Immediate verification after save - Valid: true, Length: 990

nternal/praktis/checkout/cart-utils/payments.go:351
[0.597ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144951
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_cart/calculateTotals took 272.426083ms

ackages/magento-core/mage-store/sales/quote-repository.go:83
[0.460ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144951

ackages/magento-core/mage-store/sales/quote-repository.go:83
[1.212ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE entity_id = 1144951 ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

ackages/magento-core/mage-store/sales/quote-item.go:67
[0.234ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144951

ackages/magento-core/mage-store/sales/quote.go:218
[0.233ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144951
Loading gallery for product ID: 171720

ackages/magento-core/mage-store/mage-product/product-image.go:104
[0.219ms] [rows:1] select value, label, position from catalog_product_entity_media_gallery g
    inner join catalog_product_entity_media_gallery_value go on g.value_id = go.value_id
where entity_id = 171720 and disabled = 0 and store_id in (0) order by position asc
Loaded 1 gallery images for product ID: 171720

nternal/praktis/catalog/product/product-video.go:50
[0.150ms] [rows:0]
		SELECT
			cpe.entity_id as product_id,
			iv.video_id,
			iv.title as video_title,
			iv.description as video_description,
			iv.url as video_url,
			iv.video_type,
			iv.video_store_view,
			iv.video_status,
			iv.image as video_thumbnail,
			ipv.video_position
		FROM catalog_product_entity cpe
			INNER JOIN iwd_product_video ipv ON cpe.entity_id = ipv.product_id
			INNER JOIN iwd_video iv ON ipv.video_id = iv.video_id
		WHERE cpe.entity_id = 171720
			AND iv.video_status = 1  -- Only active videos
		ORDER BY ipv.video_position


nternal/praktis/catalog/product/product-availability.go:38
[0.175ms] [rows:8] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id = 171720

nternal/praktis/catalog/product/product-availability.go:104
[0.337ms] [rows:9] SELECT * FROM `theme_praktis_store` ORDER BY display_order ASC
Found 9 active stores
Store: ID=20, Name=PRAKTIS - София MEGA, WarehouseCode=1001
Store: ID=18, Name=PRAKTIS-Пловдив, WarehouseCode=901
Store: ID=13, Name=PRAKTIS - Хасково, WarehouseCode=601
Store: ID=12, Name=PRAKTIS - Велико Търново, WarehouseCode=501
Store: ID=10, Name=PRAKTIS - Видин, WarehouseCode=201
Store: ID=11, Name=PRAKTIS - Стара Загора, WarehouseCode=401
Store: ID=15, Name=PRAKTIS - Русе, WarehouseCode=801
Store: ID=7, Name=PRAKTIS - София Хаджи Димитър, WarehouseCode=101
Store: ID=19, Name=Мебели Търговски Център-Стара Загора, WarehouseCode=420

nternal/praktis/catalog/product/product-availability.go:151
[0.279ms] [rows:8] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id in (171720)

raphql/entity/praktis_store_getter.go:52
[0.261ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '101' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.220ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '401' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.200ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '801' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.111ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.085ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '601' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.080ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '501' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.073ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '201' ORDER BY `theme_praktis_store`.`id` LIMIT 1

raphql/entity/praktis_store_getter.go:52
[0.072ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '901' ORDER BY `theme_praktis_store`.`id` LIMIT 1
[GIN] 2025/06/21 - 14:07:55 | 200 |  492.862125ms |       ********* | POST     "/graphql"
[GIN] 2025/06/21 - 14:07:57 | 204 |      19.191µs |       ********* | OPTIONS  "/graphql"

ackages/magento-core/mage-store/sales/quote-repository.go:186
[0.501ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144951

ackages/magento-core/mage-store/sales/quote-repository.go:186
[1.210ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = 'ed9501d2-22b6-49fd-9ade-b2a9e827195d' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

ackages/magento-core/mage-store/sales/quote.go:218
[0.333ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144951
BNP: Retrieving persistent payment data before order creation operations
BNP: Retrieving persistent payment data for quote 1144951
BNP: Retrieved persistent payment data (710 bytes) for quote 1144951
BNP: Persistent payment data retrieved successfully (710 bytes)

nternal/praktis/checkout/cart-utils/payments.go:423
[0.223ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144951 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1

nternal/praktis/checkout/cart-utils/address-save.go:259
[6.421ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_email`='<EMAIL>',`customer_firstname`='Stoyan',`customer_id`=0,`customer_is_guest`=0,`customer_lastname`='Atanasov',`remote_ip`='*************',`updated_at`='2025-06-21 14:07:57.658' WHERE `entity_id` = 1144951

nternal/praktis/checkout/cart-utils/address-save.go:275
[0.331ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-21 14:06:46',`updated_at`='2025-06-21 14:07:57.664',`address_type`='billing',`quote_id`=1144951,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267108
BNP: Restoring payment data for quote 1144951 (710 bytes)

nternal/praktis/checkout/cart-utils/address-save.go:275
[0.268ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-21 14:06:46',`updated_at`='2025-06-21 14:07:57.665',`address_type`='shipping',`quote_id`=1144951,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=13,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=198,`base_grand_total`=198,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267109

BNP: Current payment data exists (990 bytes), no restore needed
raphql/resolver/cart-checkout.resolvers.go:506
[0.105ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144951

raphql/entity/praktis_store_getter.go:52
[0.405ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

nternal/praktis/checkout/cart-utils/address-save.go:47
[0.592ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-21 14:06:46',`updated_at`='2025-06-21 14:07:57.666',`address_type`='billing',`quote_id`=1144951,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267108

nternal/praktis/checkout/cart-utils/address-save.go:47
[0.748ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-21 14:06:46','2025-06-21 14:07:57.666','billing',1144951,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store',NULL,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL,'0','','','','','','','','','','','','',0,0,0,0,22,2267108) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-21 14:07:57.666',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)

raphql/entity/praktis_store_getter.go:52
[0.340ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

nternal/praktis/checkout/cart-utils/address-save.go:47
[0.481ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-21 14:06:46',`updated_at`='2025-06-21 14:07:57.668',`address_type`='shipping',`quote_id`=1144951,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=13,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=198,`base_grand_total`=198,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267109
BNP: Restoring payment data for quote 1144951 (710 bytes)

nternal/praktis/checkout/cart-utils/address-save.go:47
[0.526ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-21 14:06:46','2025-06-21 14:07:57.668','shipping',1144951,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store','Вземи от обект - Вземи от обект',13,0,0,0,0,198,198,NULL,NULL,0,0,'0','','','','','','','','','','','','',0,0,0,0,22,2267109) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-21 14:07:57.668',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)
BNP: Current payment data exists (990 bytes), no restore needed

raphql/resolver/cart-checkout.resolvers.go:506
[0.125ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144951

raphql/resolver/cart-checkout.resolvers.go:341
[0.383ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_note`='' WHERE entity_id = 1144951
UpdateQuotePaymentMethod: Processing payment ID 1838697, current method: stenik_leasingjetcredit, target method: stenik_leasingjetcredit
UpdateQuotePaymentMethod: Payment method already correct for payment ID 1838697, skipping update
BNP: Restoring payment data for quote 1144951 (710 bytes)

raphql/resolver/cart-checkout.resolvers.go:506
[0.227ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144951
BNP: Current payment data exists (990 bytes), no restore needed

raphql/resolver/cart-checkout.resolvers.go:365
[0.326ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144951 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

raphql/resolver/cart-checkout.resolvers.go:377
[0.238ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144951
BNP: Database check - Valid: true, Value: a:10:{s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:5:"phone";s:10:"0883555204";s:13:"customer_data";a:11:{s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";s:9:"post_code";s:4:"1000";s:12:"company_name";s:0:"";s:3:"mol";s:0:"";s:3:"eik";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. teres";s:4:"city";s:5:"sofia";s:3:"egn";s:10:"9102288468";}s:10:"variant_id";i:481435;s:5:"names";s:15:"Stoyan Atanasov";s:13:"good_type_ids";s:3:"353";s:9:"principal";s:3:"198";s:4:"loan";a:10:{s:22:"total_repayment_amount";s:6:"205.13";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"68.38";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:21:"processing_fee_amount";s:1:"0";s:3:"apr";s:5:"23.76";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481435";}s:11:"downpayment";s:1:"0";}
BNP: Updated payment object with additional information
BNP: === VALIDATING QUOTE FOR BNP PAYMENT ===
BNP: Quote ID: 1144951
BNP: Payment ID: 1838697
BNP: Payment method: stenik_leasingjetcredit
BNP: Additional information length: 990
BNP: Additional information content: a:10:{s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:5:"phone";s:10:"0883555204";s:13:"customer_data";a:11:{s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";s:9:"post_code";s:4:"1000";s:12:"company_name";s:0:"";s:3:"mol";s:0:"";s:3:"eik";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. teres";s:4:"city";s:5:"sofia";s:3:"egn";s:10:"9102288468";}s:10:"variant_id";i:481435;s:5:"names";s:15:"Stoyan Atanasov";s:13:"good_type_ids";s:3:"353";s:9:"principal";s:3:"198";s:4:"loan";a:10:{s:22:"total_repayment_amount";s:6:"205.13";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"68.38";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:21:"processing_fee_amount";s:1:"0";s:3:"apr";s:5:"23.76";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481435";}s:11:"downpayment";s:1:"0";}
BNP: Payment data is in PHP serialized format (expected for Magento)
BNP: PHP serialized payment data validation passed
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_order/createNewOrder took 465.980479ms

BNP: Transferring payment data from quote 1144951 to order 301011
BNP: Retrieving persistent payment data for quote 1144951
ackages/magento-core/mage-store/sales/order-repository.go:61
[1.045ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '*********' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
BNP: Retrieved persistent payment data (710 bytes) for quote 1144951
BNP: Found BNP payment data (710 bytes), transferring to order payment

nternal/praktis/checkout/cart-utils/payments.go:423
[0.160ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144951 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1
BNP: Converted payment data to PHP serialized format (994 bytes)
BNP: Payment data successfully transferred to order 301011 (1 records updated)

nternal/praktis/checkout/cart-utils/payments.go:470
[1.297ms] [rows:1]
		UPDATE sales_flat_order_payment
		SET additional_information = 'a:10:{s:5:"names";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:9:"principal";s:3:"198";s:13:"good_type_ids";s:3:"353";s:4:"loan";a:10:{s:3:"apr";s:5:"23.76";s:26:"correct_downpayment_amount";s:1:"0";s:3:"nir";s:5:"21.47";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"205.13";s:18:"installment_amount";s:5:"68.38";s:8:"maturity";s:1:"3";s:18:"pricing_variant_id";s:6:"481435";}s:3:"pin";s:10:"9102288468";s:10:"variant_id";s:6:"481435";s:13:"customer_data";a:11:{s:3:"egn";s:10:"9102288468";s:10:"first_name";s:6:"Stoyan";s:9:"last_name";s:8:"Atanasov";s:7:"address";s:11:"zh.k. teres";s:12:"company_name";s:0:"";s:3:"eik";s:0:"";s:5:"email";s:26:"<EMAIL>";s:3:"mol";s:0:"";s:5:"phone";s:10:"0883555204";s:9:"post_code";s:4:"1000";s:4:"city";s:5:"sofia";}s:11:"downpayment";s:1:"0";s:5:"email";s:26:"<EMAIL>";}'
		WHERE parent_id = 301011


nternal/praktis/checkout/cart-utils/payments.go:494
[0.151ms] [rows:-]
		SELECT entity_id, additional_information
BNP: VERIFICATION PASSED - Payment data confirmed in database
BNP: Order ID: 301011, Payment ID: 301010, Data Length: 994 bytes
BNP: Data Preview: a:10:{s:5:"names";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:9:"principal";s:3:"198";s:1...
		FROM sales_flat_order_payment
		WHERE parent_id = 301011

BNP: Backup data cleaned up for quote 1144951
BNP: Payment data successfully transferred to order *********
BNP: Reloading order ********* to get updated payment information

nternal/praktis/checkout/cart-utils/payments.go:516
[0.976ms] [rows:1] DELETE FROM `bnp_payment_data_storage` WHERE quote_id = 1144951

ackages/magento-core/mage-store/sales/order-repository.go:61
[0.339ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '*********' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
BNP: Order ********* reloaded successfully
BNP: Payment not loaded by preload, manually loading payment for order 301011

raphql/resolver/cart-checkout.resolvers.go:467
[0.253ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE parent_id = 301011 ORDER BY `sales_flat_order_payment`.`entity_id` LIMIT 1
BNP: Payment manually loaded successfully - ID: 301010, Method: stenik_leasingjetcredit
Post-order processing started for order *********
BNP: Order details - ID: 301011, IncrementID: *********
BNP: Order payment found - ID: 301010, Method: stenik_leasingjetcredit
Order ********* uses BNP payment method, processing BNP data transfer and application
BNP: === POST-ORDER PAYMENT DATA VERIFICATION ===
BNP: Order: *********, Order ID: 301011, Payment ID: 301010

nternal/praktis/checkout/order-utils/bnp_post_processing.go:51
[0.129ms] [rows:-]
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = 301011

BNP: Payment data verified in database
BNP: Order: *********, Payment ID: 301010, Data Length: 994 bytes
BNP: Data Preview: a:10:{s:5:"names";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:9:"principal";s:3:"198";s:1...
BNP: Failed to parse payment data JSON: invalid character 'a' looking for beginning of value
BNP: Database verification PASSED
BNP: Admin panel will be able to display BNP applicant information
BNP: === APPLICATION SUBMISSION START ===
BNP: Order Number: *********
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: Loading order from database...

ackages/magento-core/mage-store/sales/order-repository.go:61
[0.474ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '*********' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
BNP: Order loaded successfully - ID: 301011, Status: pending, Total: 198.00
BNP: Validating payment method...
BNP: Order ********* has no payment information
Failed to submit BNP application for order *********: order has no payment information
Post-order processing failed for order *********: failed to submit BNP application: order has no payment information
[GIN] 2025/06/21 - 14:07:58 | 200 |  487.739071ms |       ********* | POST     "/graphql"
[GIN] 2025/06/21 - 14:07:58 | 204 |      27.785µs |       ********* | OPTIONS  "/graphql"
BNP: Starting BNP Calculator API initialization
BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
BNP: Using merchant ID: 433147
BNP: Using merchant ID: 433147
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Operating in mode: PRODUCTION
BNP: Starting TLS configuration setup
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
BNP: Sandbox mode resolved to: false
BNP: No key password configured (as expected)
BNP: Loading X509 certificate/key pair
BNP: Successfully loaded X509 certificate/key pair
BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
BNP: Certificate count in TLS config: 1
BNP: TLS configuration loaded successfully
BNP: === BUSINESS LOGIC ===
BNP: Operation: Parameter Validation
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: downPayment: 0
BNP: method: GetAvailablePricingSchemes
BNP: goodTypeIds: 353
BNP: principal: 198
BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
BNP: URL Parameters: [433147 353 198.00 0.00]
BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/198.00/0.00
BNP: === OUTGOING REQUEST ===
BNP: Method: GetAvailablePricingSchemes
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/198.00/0.00
BNP: HTTP Method: GET
BNP: Request Headers:
BNP:   User-Agent: MerchantPos
BNP: Request Parameters:
BNP:   Service Path: ServicesPricing
BNP:   API Method: GetAvailablePricingSchemes
BNP:   Merchant ID: 433147
BNP:   Good Type IDs: 353
BNP:   Principal: 198.00
BNP:   Down Payment: 0.00
BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/198.00/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

BNP: === INCOMING RESPONSE ===
BNP: Method: GetAvailablePricingSchemes
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: Duration: 197.850453ms
BNP: Status Code: 200
BNP: Status: 200 OK
BNP: Response Headers:
BNP:   Content-Type: application/xml; charset=utf-8
BNP:   Server: Microsoft-IIS/10.0
BNP:   X-Aspnet-Version: 4.0.30319
BNP:   X-Powered-By: ASP.NET
BNP:   Date: Sat, 21 Jun 2025 14:07:57 GMT
BNP:   Content-Length: 675
BNP:   Cache-Control: private
BNP: Response Body Length: 675 bytes
BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
BNP: Response Status: SUCCESS
BNP: === BUSINESS LOGIC ===
BNP: Operation: Response Validation
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: errorMessage:
BNP: schemeCount: 3
BNP: errorCode: 0
BNP: === BUSINESS LOGIC ===
BNP: Operation: Data Transformation
BNP: Timestamp: 2025-06-21T14:07:58Z
BNP: inputSchemes: 3
BNP: outputSchemes: 3
BNP: success: true
BNP: GetAvailablePricingSchemes completed successfully in 197.850453ms, returned 3 schemes
[GIN] 2025/06/21 - 14:07:58 | 200 |  198.829784ms |       ********* | POST     "/graphql"
