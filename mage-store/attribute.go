package mage_store

import (
	"fmt"
	"praktis.bg/store-api/packages/magento-core/storage"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

type BackendType string

const (
	AttributeTypeInt      BackendType = "int"
	AttributeTypeVarchar  BackendType = "varchar"
	AttributeTypeText     BackendType = "text"
	AttributeTypeDecimal  BackendType = "decimal"
	AttributeTypeStatic   BackendType = "static"
	AttributeTypeDatetime BackendType = "datetime"
)

type AttributeCode string

func (c AttributeCode) String() string {
	return string(c)
}

func (c AttributeCode) ToSelectCode() Select {
	return Select(":" + c)
}

type CoreAttribute struct {
	EntityType    types.EntityType
	AttributeID   int
	Position      int `gorm:"column:sort_order"`
	AttributeCode AttributeCode
	BackendType   BackendType
	FrontendLabel string // @TODO: handle label translation

	cacheKey      string `gorm:"-"`
	isCacheLoaded bool   `gorm:"-"`
}

func prepAttributeCode(code string) string {
	code = strings.ToLower(code)
	code = strings.ReplaceAll(code, ":", "")
	code = strings.TrimSpace(code)

	return code
}

func getAttributeAlias(code string, storeId int) string {
	return fmt.Sprintf("attr_%s_%d",
		prepAttributeCode(code),
		storeId,
	)
}

func getAttributeSelect(code string, storeId int) string {
	codeV := prepAttributeCode(code)
	return fmt.Sprintf("%s.value as %s",
		getAttributeAlias(codeV, storeId),
		codeV,
	)
}

func (a *CoreAttribute) GetID() int {
	return a.AttributeID
}

func (a *CoreAttribute) GetAlias(storeId int) string {
	return getAttributeAlias(a.AttributeCode.String(), storeId)
}

func (a *CoreAttribute) GetMagentoTable() storage.DbTable {
	return getTypeTable(a.BackendType, a.EntityType)
}

func getTypeTable(t BackendType, e types.EntityType) storage.DbTable {
	prefix := "unknown_"
	if e == types.ProductEntityType {
		prefix = "catalog_product"
	} else if e == types.CategoryEntityType {
		prefix = "catalog_category"
	} else if e == types.CustomerEntityType {
		prefix = "customer"
	} else if e == types.CustomerAddressEntityType {
		prefix = "customer_address"
	} else {
		panic("unknown entity type: " + e.String())
	}

	table := ""
	if t == AttributeTypeInt {
		table = prefix + "_entity_int"
	} else if t == AttributeTypeVarchar {
		table = prefix + "_entity_varchar"
	} else if t == AttributeTypeText {
		table = prefix + "_entity_text"
	} else if t == AttributeTypeDecimal {
		table = prefix + "_entity_decimal"
	} else if t == AttributeTypeStatic {
		table = prefix + "_entity"
	} else if t == AttributeTypeDatetime {
		table = prefix + "_entity_datetime"
	} else {
		panic("unknown attribute type: " + t)
	}

	return storage.DbTable(table)
}
