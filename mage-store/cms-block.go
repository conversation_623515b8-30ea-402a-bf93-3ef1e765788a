package mage_store

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

var _ cache.CacheableObject = (*CmsBlock)(nil)

type CmsBlock struct {
	BlockID    int64  `json:"block_id" gorm:"column:block_id"`
	Identifier string `json:"identifier"`
	RawContent string `gorm:"column:content"`
	Content    string `gorm:"column:compiled_content"`
	Title      string `json:"title"`
	IsActive   bool   `json:"is_active"`
	StoreID    int64  `json:"store_id"`

	cacheKey  string
	fromCache bool
}

func (c *CmsBlock) CacheKey() string {
	return fmt.Sprintf("cms_block_%s_store_%d", c.Identifier, c.StoreID)
}

func (c *CmsBlock) SetCacheKey(key string) {
	c.cacheKey = key
}

func (c *CmsBlock) IsCacheLoaded() bool {
	return c.fromCache
}
func (c *CmsBlock) CacheTTL() time.Duration {
	return cache.InfiniteTTL
}

func (c *CmsBlock) GetCacheObject() map[string]string {
	return map[string]string{
		"block_id":   fmt.Sprintf("%d", c.BlockID),
		"identifier": c.Identifier,
		"content":    c.Content,
		"title":      c.Title,
		"is_active":  fmt.Sprintf("%t", c.IsActive),
		"store_id":   fmt.Sprintf("%d", c.StoreID),
	}
}

func (c *CmsBlock) SetCacheObject(v map[string]string) error {
	if raw, ok := v["block_id"]; ok {
		c.BlockID = types.ToInt64(raw)
	}
	if raw, ok := v["identifier"]; ok {
		c.Identifier = raw
	}
	if raw, ok := v["title"]; ok {
		c.Title = raw
	}
	if raw, ok := v["content"]; ok {
		c.RawContent = raw
	}
	if raw, ok := v["is_active"]; ok {
		c.IsActive = raw == "true" || raw == "1"
	}
	if raw, ok := v["store_id"]; ok {
		c.StoreID = types.ToInt64(raw)
	}

	return nil
}

func (c *CmsBlock) TableName() string {
	return "cms_block"
}
