package mage_product

import (
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
	"sync"
	"time"
)

const (
	VisibilityInCatalog int = 2
	VisibilityInSearch      = 3
	VisibilityBoth          = 4
)

type ProductTypeID string

const (
	ProductTypeIDSimple       ProductTypeID = "simple"
	ProductTypeIDConfigurable               = "configurable"
	ProductTypeIDBundle                     = "bundle"
)

var _ mage_types.Entity[*ProductEntity] = (*ProductEntity)(nil)

type ProductEntity struct {
	lock            sync.Mutex
	loaded          bool
	cacheKey        string `gorm:"-"`
	isFullCacheLoad bool   `gorm:"-"`

	EntityID     int64         `gorm:"primaryKey;column:entity_id"`
	TypeID       ProductTypeID `gorm:"column:type_id"`
	Sku          string        `gorm:"column:sku"`
	EntityTypeID int64         `gorm:"column:entity_type_id"`
	CreatedAt    time.Time     `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt    time.Time     `gorm:"autoUpdateTime;column:updated_at"`

	StoreID *int `gorm:"-"`

	GalleryImages []GalleryImage `gorm:"-"`
	Image         GalleryImage   `gorm:"-"`

	Attributes mage_types.EntityData `gorm:"-"`
}

func (p *ProductEntity) GetID() int64 {
	return p.EntityID
}

func (p *ProductEntity) TableName() string {
	return "catalog_product_entity"
}

func (p *ProductEntity) GetEntityType() types.EntityType {
	return types.ProductEntityType
}

func (p *ProductEntity) GetStore() *magento_core.StoreEntity {
	return magento_core.GetStoreClient().GetStore()
}

func (p *ProductEntity) GetStoreID() int {
	if p.StoreID != nil {
		return *p.StoreID
	}

	store := p.GetStore()
	if store != nil {
		return store.GetID()
	}

	return 0
}

func (p *ProductEntity) New() *ProductEntity {
	return &ProductEntity{}
}

func (p *ProductEntity) NewSlice() []*ProductEntity {
	return []*ProductEntity{}
}

var ProductFieldsKeys = []string{
	"entity_id", "sku",
	"type_id", "entity_type_id",
	"created_at", "updated_at",
	//"store_code", "attributes",
}

var DefaultAttributeMap = map[string]bool{
	"entity_id":      true,
	"type_id":        true,
	"sku":            true,
	"entity_type_id": true,
	"created_at":     true,
	"updated_at":     true,
	//"store_code":     true,
	//"attributes":     true,
}

func (p *ProductEntity) AttributeIsField(key string) bool {
	val, ok := DefaultAttributeMap[key]
	return ok && val
}
