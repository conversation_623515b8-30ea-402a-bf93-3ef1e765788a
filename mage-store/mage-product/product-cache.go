package mage_product

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

func (p *ProductEntity) Cache() *cache.RedisCacheProvider {
	storeClient := magento_core.GetStoreClient()
	return storeClient.GetCacheClient()
}

func (p *ProductEntity) CacheKey() string {
	if p.cacheKey != "" {
		return p.cacheKey
	}

	return fmt.Sprintf("product:%d", p.EntityID)
}

func (p *ProductEntity) GetSkuIDMapKey() string {
	return productSkuMapKey(p.Sku)
}

func productSkuMapKey(sku string) string {
	return fmt.Sprintf("product:sku:%s", sku)
}

func (p *ProductEntity) CacheTTL() time.Duration {
	return 10 * time.Minute
}

func (p *ProductEntity) SetCacheKey(key string) {
	p.cacheKey = key
}

func (p *ProductEntity) IsCacheLoaded() bool {
	return p.isFullCacheLoad
}

func (p *ProductEntity) GetCacheObject() map[string]string {
	res := make(map[string]string)
	data, err := p.GetData()
	if err != nil {
		core_utils.ErrorWarning(err)
		return res
	}

	for k, v := range data {
		res[k] = types.ToStr(v)
	}

	return res
}

func (p *ProductEntity) SetCacheObject(cacheData map[string]string) error {
	for k, v := range cacheData {
		err := p.SetVal(k, v)
		if err != nil {
			return err
		}
	}

	p.isFullCacheLoad = true

	return nil
}
