[GIN] 2025/06/24 - 11:23:33 | 204 |      20.059µs |       ********* | OPTIONS  "/graphql"

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.187ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144973

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.704ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = 'f8e42e3d-61a4-4ac0-b74d-65355beb6bdb' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote.go:218
[0.261ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144973
[GIN] 2025/06/24 - 11:23:33 | 200 |    1.837306ms |       ********* | POST     "/graphql"
[GIN] 2025/06/24 - 11:23:33 | 204 |       14.78µs |       ********* | OPTIONS  "/graphql"
[GIN] 2025/06/24 - 11:23:33 | 204 |       9.023µs |       ********* | OPTIONS  "/graphql"

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.213ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144973

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.716ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = 'f8e42e3d-61a4-4ac0-b74d-65355beb6bdb' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-item.go:67
[0.230ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144973
2025/06/24 11:23:33 [INFO] BNP: Analyzing 1 quote items to determine good type IDs
2025/06/24 11:23:33 [DEBUG] BNP: Processing quote item 1: SKU=3520924, Qty=1.00, Price=129.99, RowTotal=129.99

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/mage-product/product-reposity-attributes.go:131
[0.245ms] [rows:1] select
    entity_id, attribute_id, value, store_id
from catalog_product_entity_int where entity_id in (210924) and attribute_id in (191) and store_id in (0, 1)
2025/06/24 11:23:33 [INFO] BNP: Using good type IDs '353' from product 3520924 (item 1) for entire quote
2025/06/24 11:23:33 [INFO] BNP: Calculating financing options for quote total: 129.99 with good type IDs: 353
2025/06/24 11:23:33 [INFO] BNP: Starting GetAvailableVariantsGroupedByPricingScheme with goodTypeIds='353', principal=129.99, downPayment=0.00
2025/06/24 11:23:33 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/24 11:23:33 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/24 11:23:33 [DEBUG] BNP: Using merchant ID: 433147
2025/06/24 11:23:33 [INFO] BNP: Using merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/24 11:23:33 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/24 11:23:33 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/24 11:23:33 [DEBUG] BNP: Starting TLS configuration setup
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/24 11:23:33 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/24 11:23:33 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/24 11:23:33 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/24 11:23:33 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/24 11:23:33 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/24 11:23:33 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: No key password configured (as expected)
2025/06/24 11:23:33 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/24 11:23:33 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/24 11:23:33 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/24 11:23:33 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/24 11:23:33 [INFO] BNP: TLS configuration loaded successfully
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00]
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00
2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip


2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.133ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144973

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.443ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = 'f8e42e3d-61a4-4ac0-b74d-65355beb6bdb' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/24 11:23:33 /graphapi/packages/magento-core/mage-store/sales/quote-item.go:67
[0.173ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144973
2025/06/24 11:23:33 [INFO] BNP: Analyzing 1 quote items to determine good type IDs
2025/06/24 11:23:33 [DEBUG] BNP: Processing quote item 1: SKU=3520924, Qty=1.00, Price=129.99, RowTotal=129.99
2025/06/24 11:23:33 [INFO] BNP: Using good type IDs '353' from product 3520924 (item 1) for entire quote
2025/06/24 11:23:33 [INFO] BNP: Calculating financing options for quote total: 129.99 with good type IDs: 353
2025/06/24 11:23:33 [INFO] BNP: Starting GetAvailableVariantsGroupedByPricingScheme with goodTypeIds='353', principal=129.99, downPayment=0.00
2025/06/24 11:23:33 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/24 11:23:33 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/24 11:23:33 [DEBUG] BNP: Using merchant ID: 433147
2025/06/24 11:23:33 [INFO] BNP: Using merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/24 11:23:33 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/24 11:23:33 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/24 11:23:33 [DEBUG] BNP: Starting TLS configuration setup
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/24 11:23:33 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/24 11:23:33 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/24 11:23:33 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/24 11:23:33 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/24 11:23:33 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/24 11:23:33 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/24 11:23:33 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/24 11:23:33 [DEBUG] BNP: No key password configured (as expected)
2025/06/24 11:23:33 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/24 11:23:33 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/24 11:23:33 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/24 11:23:33 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/24 11:23:33 [INFO] BNP: TLS configuration loaded successfully
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00]
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00
2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/129.99/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Duration: 213.4146ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 526
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 526 bytes
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Duration: 218.559989ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 526
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 526 bytes
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [DEBUG] BNP: schemeCount: 2
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [DEBUG] BNP: inputSchemes: 2
2025/06/24 11:23:33 [DEBUG] BNP: outputSchemes: 2
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 213.4146ms, returned 2 schemes
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: instalment: 0
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2791
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00 0.00 2791]
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [DEBUG] BNP: schemeCount: 2
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: inputSchemes: 2
2025/06/24 11:23:33 [DEBUG] BNP: outputSchemes: 2
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 218.559989ms, returned 2 schemes
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: instalment: 0
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2791
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00 0.00 2791]
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791
2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[7]: 2791
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[7]: 2791
2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2791 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Duration: 51.218569ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 574
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 574 bytes
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0.06</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>65.00</InstallmentAmount><Maturity>2</Maturity><NIR>0</NIR><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName><PricingVariantId>792441</PricingVariantId><TotalRepaymentAmount>129.99</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: variantCount: 1
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792441, defaulting to '0'
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: Parsed variant ID=792441, APR=0.06, Maturity=2, InstallmentAmount=65.00
2025/06/24 11:23:33 [INFO] BNP: Duration: 51.294162ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: inputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: outputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2791
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingVariants completed successfully in 51.218569ms, returned 1 variants for scheme 2791
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 574
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 574 bytes
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0.06</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>65.00</InstallmentAmount><Maturity>2</Maturity><NIR>0</NIR><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName><PricingVariantId>792441</PricingVariantId><TotalRepaymentAmount>129.99</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: instalment: 0
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2792
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00 0.00 2792]
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792
2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[7]: 2792
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [DEBUG] BNP: variantCount: 1
2025/06/24 11:23:33 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792441, defaulting to '0'
2025/06/24 11:23:33 [DEBUG] BNP: Parsed variant ID=792441, APR=0.06, Maturity=2, InstallmentAmount=65.00
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: inputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: outputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2791
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingVariants completed successfully in 51.294162ms, returned 1 variants for scheme 2791
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Parameter Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: goodTypeIds: 353
2025/06/24 11:23:33 [DEBUG] BNP: principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP: downPayment: 0
2025/06/24 11:23:33 [DEBUG] BNP: instalment: 0
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2792
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/24 11:23:33 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP: URL Parameters: [433147 353 129.99 0.00 0.00 2792]
2025/06/24 11:23:33 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792
2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/24 11:23:33 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792
2025/06/24 11:23:33 [INFO] BNP: HTTP Method: GET
2025/06/24 11:23:33 [DEBUG] BNP: Request Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/24 11:23:33 [DEBUG] BNP: Request Parameters:
2025/06/24 11:23:33 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/24 11:23:33 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [DEBUG] BNP:   Merchant ID: 433147
2025/06/24 11:23:33 [DEBUG] BNP:   Good Type IDs: 353
2025/06/24 11:23:33 [DEBUG] BNP:   Principal: 129.99
2025/06/24 11:23:33 [DEBUG] BNP:   Down Payment: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/24 11:23:33 [DEBUG] BNP:   Additional Param[7]: 2792
2025/06/24 11:23:33 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/129.99/0.00/0.00/2792 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Duration: 49.572086ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 571
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 571 bytes
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>43.33</InstallmentAmount><Maturity>3</Maturity><NIR>0</NIR><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName><PricingVariantId>792443</PricingVariantId><TotalRepaymentAmount>129.99</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [DEBUG] BNP: variantCount: 1
2025/06/24 11:23:33 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792443, defaulting to '0'
2025/06/24 11:23:33 [DEBUG] BNP: Parsed variant ID=792443, APR=0, Maturity=3, InstallmentAmount=43.33
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: inputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: outputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2792
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingVariants completed successfully in 49.572086ms, returned 1 variants for scheme 2792
2025/06/24 11:23:33 [INFO] BNP: Successfully calculated financing options for quote total 129.99, returning 2 variant groups
[GIN] 2025/06/24 - 11:23:33 | 200 |   322.00531ms |       ********* | POST     "/graphql"
2025/06/24 11:23:33 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/24 11:23:33 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [INFO] BNP: Duration: 49.805959ms
2025/06/24 11:23:33 [INFO] BNP: Status Code: 200
2025/06/24 11:23:33 [INFO] BNP: Status: 200 OK
2025/06/24 11:23:33 [DEBUG] BNP: Response Headers:
2025/06/24 11:23:33 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/24 11:23:33 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/24 11:23:33 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/24 11:23:33 [DEBUG] BNP:   Date: Tue, 24 Jun 2025 11:23:32 GMT
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Length: 571
2025/06/24 11:23:33 [DEBUG] BNP:   Cache-Control: private
2025/06/24 11:23:33 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Length: 571 bytes
2025/06/24 11:23:33 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>43.33</InstallmentAmount><Maturity>3</Maturity><NIR>0</NIR><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName><PricingVariantId>792443</PricingVariantId><TotalRepaymentAmount>129.99</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/24 11:23:33 [INFO] BNP: Response Status: SUCCESS
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Response Validation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: variantCount: 1
2025/06/24 11:23:33 [DEBUG] BNP: errorCode: 0
2025/06/24 11:23:33 [DEBUG] BNP: errorMessage:
2025/06/24 11:23:33 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792443, defaulting to '0'
2025/06/24 11:23:33 [DEBUG] BNP: Parsed variant ID=792443, APR=0, Maturity=3, InstallmentAmount=43.33
2025/06/24 11:23:33 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/24 11:23:33 [INFO] BNP: Operation: Data Transformation
2025/06/24 11:23:33 [INFO] BNP: Timestamp: 2025-06-24T11:23:33Z
2025/06/24 11:23:33 [DEBUG] BNP: outputVariants: 1
2025/06/24 11:23:33 [DEBUG] BNP: schemeId: 2792
2025/06/24 11:23:33 [DEBUG] BNP: success: true
2025/06/24 11:23:33 [DEBUG] BNP: inputVariants: 1
2025/06/24 11:23:33 [INFO] BNP: GetAvailablePricingVariants completed successfully in 49.805959ms, returned 1 variants for scheme 2792
2025/06/24 11:23:33 [INFO] BNP: Successfully calculated financing options for quote total 129.99, returning 2 variant groups
[GIN] 2025/06/24 - 11:23:33 | 200 |   316.22267ms |       ********* | POST     "/graphql"
