# Reconstructed Dockerfile for todor0pfg/env:nginx1.23
# Based on Docker history analysis

FROM nginx:1.23.3-alpine

# Set environment variables
ENV NGINX_VERSION=1.23.3
ENV PKG_RELEASE=1
ENV NJS_VERSION=0.7.9

# Install additional packages
RUN apk update && apk add --no-cache \
    bash \
    coreutils \
    bash-completion \
    openssl \
    curl \
    wget \
    vim

# Install nginx modules (xslt, geoip, image-filter, njs)
RUN set -x \
    && apkArch="$(cat /etc/apk/arch)" \
    && nginxPackages=" \
        nginx=${NGINX_VERSION}-r${PKG_RELEASE} \
        nginx-module-xslt=${NGINX_VERSION}-r${PKG_RELEASE} \
        nginx-module-geoip=${NGINX_VERSION}-r${PKG_RELEASE} \
        nginx-module-image-filter=${NGINX_VERSION}-r${PKG_RELEASE} \
        nginx-module-njs=${NGINX_VERSION}.${NJS_VERSION}-r${PKG_RELEASE} \
    " \
    && apk add --no-cache --virtual .checksum-deps openssl \
    && case "$apkArch" in \
        x86_64|aarch64) \
            set -x \
            && KEY_SHA512="e09fa32f0a0eab2b879ccbbc4d0e4fb9751486eedda75e35fac65802cc9faa266425edf83e261137a2f4d16281ce2c1a5f4502930fe75154723da014214f0655" \
            && wget -O /tmp/nginx_signing.rsa.pub https://nginx.org/keys/nginx_signing.rsa.pub \
            && if echo "$KEY_SHA512 */tmp/nginx_signing.rsa.pub" | sha512sum -c -; then \
                echo "key verification succeeded!"; \
                mv /tmp/nginx_signing.rsa.pub /etc/apk/keys/; \
            else \
                echo "key verification failed!"; \
                exit 1; \
            fi \
            && apk add -X "https://nginx.org/packages/mainline/alpine/v$(egrep -o '^[0-9]+\.[0-9]+' /etc/alpine-release)/main" --no-cache $nginxPackages \
            ;; \
        *) \
            # Build from source for other architectures \
            set -x \
            && tempDir="$(mktemp -d)" \
            && chown nobody:nobody $tempDir \
            && apk add --no-cache --virtual .build-deps \
                gcc libc-dev make openssl-dev pcre2-dev zlib-dev \
                linux-headers libxslt-dev gd-dev geoip-dev libedit-dev \
                bash alpine-sdk findutils \
            && su nobody -s /bin/sh -c " \
                export HOME=${tempDir} \
                && cd ${tempDir} \
                && curl -f -O https://hg.nginx.org/pkg-oss/archive/${NGINX_VERSION}-${PKG_RELEASE}.tar.gz \
                && PKGOSSCHECKSUM=\"52a80f6c3b3914462f8a0b2fbadea950bcd79c1bd528386aff4c28d5a80c6920d783575a061a47b60fea800eef66bf5a0178a137ea51c37277fe9c2779715990 *${NGINX_VERSION}-${PKG_RELEASE}.tar.gz\" \
                && if [ \"\$(openssl sha512 -r ${NGINX_VERSION}-${PKG_RELEASE}.tar.gz)\" = \"\$PKGOSSCHECKSUM\" ]; then \
                    echo \"pkg-oss tarball checksum verification succeeded!\"; \
                else \
                    echo \"pkg-oss tarball checksum verification failed!\"; \
                    exit 1; \
                fi \
                && tar xzvf ${NGINX_VERSION}-${PKG_RELEASE}.tar.gz \
                && cd pkg-oss-${NGINX_VERSION}-${PKG_RELEASE} \
                && cd alpine \
                && make module-geoip module-image-filter module-njs module-xslt \
                && apk index -o ${tempDir}/packages/alpine/${apkArch}/APKINDEX.tar.gz ${tempDir}/packages/alpine/${apkArch}/*.apk \
                && abuild-sign -k ${tempDir}/.abuild/abuild-key.rsa ${tempDir}/packages/alpine/${apkArch}/APKINDEX.tar.gz \
                " \
            && cp ${tempDir}/.abuild/abuild-key.rsa.pub /etc/apk/keys/ \
            && apk del .build-deps \
            && apk add -X ${tempDir}/packages/alpine/ --no-cache $nginxPackages \
            ;; \
    esac \
    && apk del .checksum-deps \
    && if [ -n "$tempDir" ]; then rm -rf "$tempDir"; fi \
    && if [ -n "/etc/apk/keys/abuild-key.rsa.pub" ]; then rm -f /etc/apk/keys/abuild-key.rsa.pub; fi \
    && if [ -n "/etc/apk/keys/nginx_signing.rsa.pub" ]; then rm -f /etc/apk/keys/nginx_signing.rsa.pub; fi \
    && apk add --no-cache curl ca-certificates

# Prepare nginx directories and remove default config
RUN rm /etc/nginx/nginx.conf \
    && mkdir /etc/nginx/sites-enabled \
    && mkdir /etc/nginx/snippets/ \
    && mkdir /etc/nginx/cert/

# Copy custom nginx configuration
COPY images/.env/nginx/nginx.conf /etc/nginx/nginx.conf
COPY images/.env/nginx/fastcgi-php.conf /etc/nginx/snippets/fastcgi-php.conf

# Configure logging for Docker
RUN ln -sf /dev/stdout /var/log/nginx/access.log \
    && ln -sf /dev/stderr /var/log/nginx/error.log

# Remove default nginx config
RUN rm -f /etc/nginx/conf.d/default.conf

# Create SSL certificate directory and generate self-signed certificates
WORKDIR /etc/nginx/cert/
RUN mkdir -p /etc/nginx/cert/

# Generate self-signed SSL certificates
RUN openssl req -x509 -nodes -new -sha256 -days 1024 -newkey rsa:2048 \
    -keyout localhost.key -out RootCA.pem \
    -subj "/C=BG/CN=localhost"

RUN openssl x509 -outform pem -in RootCA.pem -out localhost.crt

# Remove default config again (seems to be done twice in original)
RUN rm -f /etc/nginx/conf.d/default.conf

# Set working directory
WORKDIR /var/www/

# Add www-data user and group
RUN set -x ; \
    addgroup -g 82 -S www-data ; \
    adduser -u 82 -D -S -G www-data www-data

# Copy custom bashrc files
COPY images/.env/.bashrc /home/<USER>/.bashrc
COPY images/.env/.bashrc /root/.bashrc

# Set the default command
CMD ["nginx", "-g", "daemon off;"]
