package customer

import (
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/packages/magento-core/auth"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

const CustomerHeader = "x-auth-customer"

func NewCustomerToken(t string) (*CustomerToken, error) {
	token, err := auth.JWTToken(t).GetClaims()
	if err != nil {
		return &CustomerToken{}, err
	}

	return &CustomerToken{
		CustomerID: types.ToInt64(token["customer_id"]),
		Name:       types.ToStr(token["name"]),
		GroupID:    types.ToInt64(token["group_id"]),
		CreatedAt:  types.ToInt64(token["created"]),
		ExpiresAt:  types.ToInt64(token["exp"]),
	}, nil
}

type CustomerToken struct {
	CustomerID int64  `json:"customer_id"`
	Name       string `json:"name"`
	GroupID    int64  `json:"group_id"`
	CreatedAt  int64  `json:"created_at"`
	ExpiresAt  int64  `json:"expires_at"`
}

func (c *CustomerToken) Validate() error {
	if c.CustomerID < 1 || c.Name == "" {
		return fmt.Errorf("customer id or name is required")
	} else if c.ExpiresAt < time.Now().Unix() {
		return fmt.Errorf("token expired at %s", time.Unix(c.ExpiresAt, 0).Format(time.RFC3339))
	}

	return nil
}

func (c *CustomerToken) ToJWTToken() (auth.JWTToken, error) {
	return auth.GetNewToken(jwt.MapClaims{
		"customer_id": c.CustomerID,
		"name":        c.Name,
		"group_id":    c.GroupID,
		"created":     c.CreatedAt,
		"exp":         c.ExpiresAt,
	})
}

func getCustomerTokenDuration() time.Duration {
	minutes := praktis.GetPraktisStore().GetConfigInt("web/cookie/cookie_lifetime", 3600*2)
	return time.Second * time.Duration(minutes)
}

func (c *MagentoCustomer) GetToken() *CustomerToken {
	if c.token == nil {
		var err error
		c.token, err = c.NewToken()
		core_utils.ErrorWarning(err)
		if c.token == nil {
			c.token = &CustomerToken{}
		}
	}

	return c.token
}

func (c *MagentoCustomer) GetTokenCacheKey() string {
	return fmt.Sprintf("customer:%d:token_login", c.EntityID)
}

func (c *MagentoCustomer) SaveToken() (err error) {
	tokenObj := c.GetToken()
	if err = tokenObj.Validate(); err != nil {
		return err
	}

	jwtValue, err := tokenObj.ToJWTToken()
	core_utils.ErrorWarning(err)
	if jwtValue != "" {
		return c.Cache().Save(
			c.GetTokenCacheKey(), jwtValue.String(), getCustomerTokenDuration(),
		)
	}

	return err
}

func (c *MagentoCustomer) ClearToken() (bool, error) {
	key := c.GetTokenCacheKey()
	if c.Cache().MustExists(key) {
		return true, c.Cache().Delete(key)
	}

	return true, nil
}

func (c *MagentoCustomer) TokenIsInCache(token string) bool {
	key := c.GetTokenCacheKey()
	if c.Cache().MustExists(key) {
		if val, err := c.Cache().Get(key); err == nil {
			return val == token
		}
	}

	return false
}

func (c *MagentoCustomer) NewToken() (*CustomerToken, error) {
	return &CustomerToken{
		CustomerID: c.EntityID,
		Name:       c.Email,
		GroupID:    c.GroupID,
		CreatedAt:  time.Now().Unix(),
		ExpiresAt: time.Now().Add(
			getCustomerTokenDuration(),
		).Unix(),
	}, nil
}
