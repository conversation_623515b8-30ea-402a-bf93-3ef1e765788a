package product

import (
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
)

func (p *PraktisProduct) MustGetVal(key string) string {
	if p == nil {
		core_utils.ErrorWarning(fmt.Errorf("attempt to call MustGetVal(%s) on nil product", key))
		return ""
	}

	val, _ := p.GetVal(key)
	return val
}

func (p *PraktisProduct) GetVal(key string) (string, error) {
	if p == nil {
		return "", fmt.Errorf("attempt to call GetVal(%s) on nil product", key)
	}

	_key := strings.ToLower(key)
	switch _key {
	case "entity_id", "id", "entityid":
		return strconv.FormatInt(p.EntityID, 10), nil
	case "name":
		return p.Name, nil
	case "thumbnail":
		return p.Thumbnail, nil
	case "sku":
		return p.Sku, nil
	case "url_key":
		return p.URLKey, nil
	case "price":
		return strconv.FormatFloat(p.Price, 'f', -1, 64), nil
	case "special_price":
		return strconv.FormatFloat(p.SpecialPrice, 'f', -1, 64), nil
	case "special_from":
		return types.DateToString(&p.SpecialFrom), nil
	case "special_to":
		return types.DateToString(&p.SpecialTo), nil
	case "brand_id":
		return strconv.Itoa(p.BrandID), nil
	case "brand":
		if p.Brand.Name != "" {
			return p.Brand.String(), nil
		}
		return "", nil
	case "in_brochure":
		return strconv.FormatBool(p.InBrochure), nil
	case "price_group":
		return strconv.Itoa(p.PriceGroupType), nil
	case "has_free_shipping":
		return strconv.FormatBool(p.HasFreeShipping), nil
	case "energy_label":
		if p.EnergyLabel != nil {
			return p.EnergyLabel.String(), nil
		}

		return "", nil
	default:
		return p.GetEntity().GetVal(_key)
	}
}

func (p *PraktisProduct) GetData(keys ...string) (mage_types.EntityData, error) {
	if len(keys) == 0 {
		res := mage_types.EntityData{
			"entity_id":         strconv.FormatInt(p.EntityID, 10),
			"product_type":      fmt.Sprintf("%d", p.ProductType),
			"sku":               p.Sku,
			"name":              p.Name,
			"thumbnail":         p.Thumbnail,
			"url_key":           p.URLKey,
			"price":             strconv.FormatFloat(p.Price, 'f', -1, 64),
			"special_price":     strconv.FormatFloat(p.SpecialPrice, 'f', -1, 64),
			"special_from":      types.DateToString(&p.SpecialFrom),
			"special_to":        types.DateToString(&p.SpecialTo),
			"brand_id":          strconv.Itoa(p.BrandID),
			"in_brochure":       strconv.FormatBool(p.InBrochure),
			"price_group":       strconv.Itoa(p.PriceGroupType),
			"has_free_shipping": strconv.FormatBool(p.HasFreeShipping),
			//"energy_label":      p.EnergyLabel.String(),
		}

		if p.Brand.Name != "" {
			res["brand"] = p.Brand.String()
		}

		data, err := p.GetEntity().GetData()
		core_utils.ErrorWarning(err)

		for k, v := range data {
			if _, ok := res[k]; !ok {
				res[k] = v
			}
		}

		return res, nil
	} else {
		res := make(mage_types.EntityData)
		var notFound []string
		for _, key := range keys {
			val, err := p.GetVal(key)
			if errors.Is(err, mage_types.FieldNotFound) {
				notFound = append(notFound, key)
			} else if err != nil {
				return nil, err
			}

			res[key] = val
		}

		if len(notFound) > 0 {
			return res, fmt.Errorf("no such key: %s", strings.Join(notFound, ","))
		}

		return res, nil
	}
}

func (p *PraktisProduct) SetVal(key string, val interface{}) error {
	var err error
	switch strings.ToLower(key) {
	case "entity_id", "id", "entityid":
		p.EntityID = types.ToInt64(val)
	case "product_type":
		p.ProductType = TypeID(types.ToInt(val))
	case "sku":
		p.Sku = types.ToStr(val)
	case "name":
		p.Name = types.ToStr(val)
	case "thumbnail":
		p.Thumbnail = types.ToStr(val)
	case "url_key":
		p.URLKey = types.ToStr(val)
	case "price":
		p.Price = types.ToFloat(val)
	case "special_price":
		p.SpecialPrice = types.ToFloat(val)
	case "special_from":
		p.SpecialFrom = types.ToDateTime(val)
	case "special_to":
		p.SpecialTo = types.ToDateTime(val)
	case "brand_id":
		p.BrandID = types.ToInt(val)
	case "brand":
		switch v := val.(type) {
		case string:
			p.Brand, err = strToBrand(v)
			core_utils.ErrorWarning(err)
		case Brand:
			p.Brand = v
		case *Brand:
			p.Brand = *v
		}
	case "in_brochure":
		p.InBrochure = types.ToBool(val)
	case "price_group":
		p.PriceGroupType = types.ToInt(val)
	case "has_free_shipping":
		p.HasFreeShipping = types.ToBool(val)
	default:
		return p.GetEntity().SetVal(key, val)
	}

	return nil
}
