package gdpr

import (
	"praktis.bg/store-api/graphql/model"
	"strings"
)

type CookieGroup struct {
	ID            int    `gorm:"column:id"`
	Title         string `gorm:"column:title"`
	Text          string `gorm:"column:text"`
	Grants        string `gorm:"column:grants"`
	Vendors       string `gorm:"column:vendors"`
	CookiePattern string `gorm:"column:cookie_pattern"`

	// Has many relationship
	CookieVendors []*CookieVendor `gorm:"foreignKey:GroupID;references:ID"`
}

func (*CookieGroup) TableName() string {
	return "pfg_cookieconsent_cookie_groups"
}

func (cg *CookieGroup) GetCookiePattern() []string {
	var cookies []string
	for _, cookie := range strings.Split(cg.CookiePattern, ",") {
		cookies = append(cookies, strings.TrimSpace(cookie))
	}

	return cookies
}

func (cg *CookieGroup) GetGrantList() []model.GDPRGrants {
	var grants []model.GDPRGrants
	for _, grant := range strings.Split(strings.ToLower(cg.<PERSON><PERSON>), ",") {
		g := model.GDPRGrants(strings.TrimSpace(grant))
		if g.<PERSON>() {
			grants = append(grants, g)
		}
	}

	return grants
}

func (cg *CookieGroup) GetVendorsList() []model.CookieVendorKey {
	var vendors []model.CookieVendorKey
	for _, grant := range strings.Split(strings.ToLower(cg.Vendors), ",") {
		v := model.CookieVendorKey(strings.TrimSpace(grant))
		if v.IsValid() {
			vendors = append(vendors, v)
		}
	}

	return vendors
}

type CookieVendor struct {
	ID          int    `gorm:"column:id"`
	GroupID     int    `gorm:"column:group_id"`
	Name        string `gorm:"column:name"`
	URL         string `gorm:"column:url"`
	CookieCount int    `gorm:"column:cookie_count"`

	// Belongs to relationship
	Group CookieGroup `gorm:"foreignKey:GroupID;references:ID"`

	// Has many relationship
	CookieDetails []*CookieDetail `gorm:"foreignKey:VendorID;references:ID"`
}

func (CookieVendor) TableName() string {
	return "pfg_cookieconsent_cookie_vendor"
}

type CookieDetail struct {
	ID         int    `gorm:"column:id"`
	VendorID   int    `gorm:"column:vendor_id"`
	Name       string `gorm:"column:name"`
	Text       string `gorm:"column:text"`
	Expiration string `gorm:"column:expiration"`
	Type       string `gorm:"column:type"`

	// Belongs to relationship
	Vendor CookieVendor `gorm:"foreignKey:VendorID;references:ID"`
}

func (CookieDetail) TableName() string {
	return "pfg_cookieconsent_cookie_details"
}
