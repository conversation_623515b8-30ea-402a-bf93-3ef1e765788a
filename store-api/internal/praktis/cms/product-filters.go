package cms

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	gorm "gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"strings"
)

type ProductFilter struct {
	AttributeCode string `json:"attribute_code"`
	Condition     string `json:"condition"`
	Value         string `json:"value"`
}

const (
	FilterEntityID        = "entity_id"
	FilterSku             = "sku"
	FilterAttributeSetID  = "attribute_set_id"
	FilterIsNew           = "_is_new"
	FilterDiscountPercent = "_discount_percent"
	FilterCategoryID      = "category_id"
)

var allSpecialFilters = []string{
	FilterEntityID,
	FilterSku,
	FilterAttributeSetID,
	FilterIsNew,
	FilterDiscountPercent,
}

const (
	ConditionEqual        = "="
	ConditionNotEqual     = "!="
	ConditionGreaterEqual = ">="
	ConditionLessEqual    = "<="
	ConditionGreaterThan  = ">"
	ConditionLessThan     = "<"
	ConditionIn           = "()"
	ConditionNotIn        = "!()"
)

var allConditions = []string{
	ConditionEqual,
	ConditionNotEqual,
	ConditionGreaterEqual,
	ConditionLessEqual,
	ConditionGreaterThan,
	ConditionLessThan,
	ConditionIn,
	ConditionNotIn,
}

func applyFilter(qb *gorm.DB, filter ProductFilter) *gorm.DB {
	switch filter.AttributeCode {
	case FilterCategoryID:
		qb = qb.Joins("JOIN catalog_category_product_index ccp ON ccp.product_id = p.entity_id").
			Where("ccp.category_id = ?", filter.getVal())
	case FilterSku, FilterEntityID, FilterAttributeSetID:
		qb = qb.Where(
			fmt.Sprintf("p.%s %s ?",
				filter.AttributeCode,
				filter.getCondition(),
			),
			filter.getVal(),
		)
	case FilterIsNew:
		// TODO: Implement this filter
	case FilterDiscountPercent:
		// TODO: Implement this filter
	default:
		attr, err := mage_store.ProductAttributes.GetAttribute(filter.AttributeCode)
		if err != nil || attr.AttributeID < 1 {
			core_utils.ErrorWarning(err)
			return qb
		}
		storeId := praktis.GetPraktisStore().StoreID
		qb = qb.Joins(fmt.Sprintf(
			"JOIN `%s` %s ON %s.entity_id = p.entity_id AND %s.attribute_id = %d AND %s.store_id in (0,%d)",
			attr.GetMagentoTable(),
			attr.GetAlias(storeId),
			attr.GetAlias(storeId),
			attr.GetAlias(storeId),
			attr.AttributeID,
			attr.GetAlias(storeId),
			storeId,
		)).Where(
			fmt.Sprintf("%s.value %s ?",
				attr.GetAlias(storeId),
				filter.getCondition(),
			),
			filter.getVal(),
		)
	}

	return qb
}

func (p ProductFilter) getCondition() string {
	switch p.Condition {
	case ConditionEqual:
		return "="
	case ConditionNotEqual:
		return "!="
	case ConditionGreaterEqual:
		return ">="
	case ConditionLessEqual:
		return "<="
	case ConditionGreaterThan:
		return ">"
	case ConditionLessThan:
		return "<"
	case ConditionIn:
		return "in"
	case ConditionNotIn:
		return "not in"
	default:
		return "="
	}
}

func (p ProductFilter) getVal() interface{} {
	switch p.Condition {
	case ConditionIn, ConditionNotIn:
		if strings.Contains(p.Value, ",") {
			return strings.Split(p.Value, ",")
		}
		return []string{p.Value}
	default:
		return p.Value
	}
}
