package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"praktis.bg/store-api/internal/praktis/catalog/product"

	"praktis.bg/store-api/graphql/model"
)

// SkuAvailability is the resolver for the skuAvailability field.
func (r *queryResolver) SkuAvailability(ctx context.Context, sku string) ([]*model.StoreAvailabilityItem, error) {
	return product.GetStoreAvailabilityModel(sku)
}
