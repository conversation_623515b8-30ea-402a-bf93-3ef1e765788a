extend type Query {
  search(searchQuery: String!): SearchResults!
  searchPage(searchQuery: String!, query: [QueryParam]): SearchPage!
}

type SearchResults {
  popularTerms: [String!]!
  categories: [SearchCategory!]!
  totalItems: Int!
  products: [Product!]!
  block: C<PERSON><PERSON><PERSON> @goField(forceResolver: true)
}

type SearchCategory {
  name: String!
  url: String!
}

type SearchPage {
  status: PageStatus!
  state: CatalogState!
  title: String!
  data: SearchResults!
}
