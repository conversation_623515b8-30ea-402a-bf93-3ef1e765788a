docker-compose yaml
```yaml
# context mage-store folder
  store-api:
    container_name: praktis-mage-store-api
    image: ecometal-graphapi-dev
    build:
      context: ./services/.env
      dockerfile: ./dev/storeApi.Dockerfile
    volumes:
      - ./services/.env/cache/gp:/go:rw
      - ./services/.env/cache/temp:/temp:rw
      - ./services/.env/dev/.storeApi_aliases:/home/<USER>/.bash_aliases:rw
      - ./services/mage-store-api:/app
      - ./services/packages:/packages
    ports:
      - "9420:9420"
    networks:
      - praktis-internal
```